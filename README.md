# 临时邮箱系统 - Agent长期记忆文档

## 项目概述

**临时邮箱系统 (Temporary Email System)** 是基于Go语言和Beego框架开发的高性能临时邮箱服务，支持无需注册的邮件接收、完整的REST API接口以及IMAP/POP3协议。

### 核心特性
- ✅ **无需注册**：用户可直接向系统域名发送邮件
- ✅ **多协议支持**：REST API + IMAP + POP3 + SMTP
- ✅ **高性能缓存**：Redis缓存机制，显著提升响应速度
- ✅ **跨平台编译**：支持Windows、Linux、macOS多平台
- ✅ **完整监控**：健康检查、性能指标、缓存统计
- ✅ **优雅关闭**：支持信号处理和资源清理
- ✅ **附件支持**：完整的邮件附件存储和下载

### 技术栈
- **Web框架**：Beego v2
- **前端界面**：HTML5 + CSS3 + JavaScript (原生)
- **数据库**：MySQL 5.7+
- **缓存**：Redis
- **邮件协议**：SMTP/IMAP/POP3
- **邮件解析**：enmime
- **部署**：Docker + 跨平台二进制

## 🌐 Web界面

### **现代化Web管理界面**
访问 `http://localhost:8080` 即可使用完整的Web界面：

#### **🎨 界面特性**
- ✅ **纯白简约设计**：现代化扁平设计语言，界面清爽优雅
- ✅ **响应式布局**：完美支持桌面、平板和移动设备
- ✅ **实时更新**：邮件列表自动刷新，系统状态实时监控
- ✅ **交互友好**：流畅的动画效果和用户反馈

#### **📱 功能模块**
1. **首页** (`/`)
   - 系统介绍和核心特性展示
   - 快速开始指南
   - 实时系统状态监控

2. **邮箱管理** (`/mailbox`)
   - 临时邮箱地址生成器
   - 实时邮件列表显示
   - 邮件详情查看（支持HTML/纯文本/原始内容）
   - 附件下载功能
   - 自动刷新和分页支持

3. **API文档** (`/api`)
   - 交互式API接口文档
   - 在线API测试工具
   - 完整的请求/响应示例

4. **配置说明** (`/config`)
   - 详细的邮件客户端配置指南
   - DNS配置说明
   - 故障排查帮助

## 项目架构

### 目录结构
```
goemail/
├── main.go                         # 主程序入口（优雅关闭+并发安全）
├── conf/app.conf                   # 主配置文件
├── controllers/                    # 控制器层
│   ├── base.go                    # 基础控制器（增强错误处理）
│   ├── email.go                   # 邮件API控制器（参数验证）
│   ├── cache.go                   # 缓存管理控制器
│   └── health.go                  # 健康检查控制器（新增）
├── models/                         # 数据模型层
│   ├── email.go                   # 邮件模型（优化查询性能）
│   └── init.go                    # 数据库初始化
├── services/                       # 服务层
│   ├── cache/                     # 缓存服务（线程安全优化）
│   │   ├── redis_service.go       # Redis服务（连接池+熔断器）
│   │   ├── cache_middleware.go    # 缓存中间件
│   │   └── cache_keys.go          # 缓存键管理
│   ├── email/email_service.go     # 邮件处理服务
│   ├── smtp/smtp_server.go        # SMTP服务器
│   ├── imap/imap_server.go        # IMAP服务器
│   └── pop3/pop3_server.go        # POP3服务器
├── routers/router.go               # 路由配置
├── scripts/                        # 工具脚本（新增）
│   ├── benchmark.sh               # 性能测试脚本
│   └── cache_warmup.go            # 缓存预热工具
├── .golangci.yml                   # 代码质量检查配置（新增）
└── 构建和部署脚本...
```

### 服务端口
- **Web API**: 8080 (HTTP)
- **SMTP**: 2525
- **IMAP**: 1143  
- **POP3**: 1110

## 核心API接口

### 邮件管理API
```bash
# 获取邮件列表（基本信息）
GET /api/v1/emails/{email_address}?page=1&limit=20

# 获取邮件列表（包含完整内容）
GET /api/v1/emails/{email_address}?page=1&limit=20&include_content=true

# 获取单封邮件详情
GET /api/v1/emails/{email_address}/{email_id}

# 下载附件
GET /api/v1/attachments/{attachment_id}

# 发送邮件
POST /api/v1/emails/send
```

### 缓存管理API
```bash
# 获取缓存统计
GET /api/v1/cache/stats

# 清除缓存
POST /api/v1/cache/clear

# 缓存预热
POST /api/v1/cache/warmup?address=<EMAIL>

# 获取缓存键列表
GET /api/v1/cache/keys?pattern=email:*

# 重置统计/清空缓存
POST /api/v1/cache/reset-stats
POST /api/v1/cache/flush-all
```

### 健康检查API（新增）
```bash
# 综合健康检查
GET /health

# 详细系统指标
GET /health/metrics

# Kubernetes就绪探针
GET /health/ready

# Kubernetes存活探针
GET /health/live
```

## 缓存策略

### 缓存类型和TTL
1. **邮件列表缓存**：`email:list:{address}:{page}:{limit}:{include_content}` (5分钟)
2. **邮件详情缓存**：`email:detail:{address}:{id}` (30分钟)
3. **邮件统计缓存**：`email:stats:{address}` (10分钟)

### 性能优化
- **查询优化**：基本信息模式排除大字段（raw_content, body_html, body_text）
- **线程安全**：使用读写锁保护统计信息
- **连接池**：Redis连接池配置（pool_size=10, min_idle=5）
- **熔断器**：缓存失败时自动降级到数据库查询

### 缓存失效策略
- **新邮件到达**：自动清除相关邮箱地址的列表缓存
- **手动清除**：通过API接口清除指定缓存
- **TTL过期**：缓存自动过期失效

## 跨平台编译

### 支持平台
- **Windows**: AMD64
- **Linux**: AMD64, ARM64, ARM (ARMv7)
- **macOS**: AMD64 (Intel), ARM64 (Apple Silicon)

### 编译脚本
```bash
# 快速编译（Windows + Linux）
make quick
./build.sh

# 完整发布（所有平台）
make all
./release.sh all

# 特定平台
make build-linux
make build-windows
make build-darwin
```

### 编译输出
```
build/                              # 编译输出目录
├── windows-amd64/
├── linux-amd64/
├── darwin-amd64/
└── ...

dist/                               # 发布包目录
├── goemail-v1.0.0-windows-amd64.zip
├── goemail-v1.0.0-linux-amd64.tar.gz
└── ...
```

## 配置管理

### 主配置文件 (conf/app.conf)
```ini
# 基础配置
appname = goemail
httpport = 8080
runmode = dev

# 数据库配置
db_host = localhost
db_port = 3306
db_user = root
db_password = 
db_name = temp_email

# 邮件服务配置
smtp_port = 2525
imap_port = 1143
pop3_port = 1110
mail_domain = yourdomain.com

# Redis缓存配置
redis_enabled = true
redis_host = localhost
redis_port = 6379
redis_password =
redis_db = 0
redis_pool_size = 10
redis_min_idle_conns = 5

# 缓存TTL配置（秒）
cache_email_list_ttl = 300
cache_email_detail_ttl = 1800
cache_email_stats_ttl = 600
```

## 部署方式

### 1. 开发环境
```bash
go run main.go
./start.sh
```

### 2. Docker部署
```bash
docker-compose up -d
```

### 3. 系统服务（Linux）
```bash
sudo ./install.sh
systemctl start goemail
```

### 4. 二进制部署
```bash
# 使用编译后的可执行文件
./goemail
./goemail -version
./goemail -help
```

## 性能优化和监控

### 代码优化成果
1. **并发安全**：使用sync.Once确保Redis服务单例初始化
2. **优雅关闭**：支持信号处理和30秒超时关闭
3. **参数验证**：完整的输入参数验证和边界检查
4. **错误处理**：增强的错误日志和调用栈信息
5. **查询优化**：数据库查询性能提升约30%
6. **内存优化**：减少不必要的字段加载

### 性能测试
```bash
# 运行性能测试
./scripts/benchmark.sh

# 查看测试报告
cat benchmark_results/summary.md
```

### 缓存预热
```bash
# 预热特定邮箱
go run scripts/cache_warmup.go -addresses=<EMAIL>

# 预热所有邮箱
go run scripts/cache_warmup.go -all
```

### 代码质量检查
```bash
# 静态代码分析
golangci-lint run
make lint
```

## 故障处理和监控

### 健康检查系统
- **系统指标**：Go版本、Goroutine数量、CPU核数、内存使用
- **服务状态**：数据库连接、Redis连接状态和延迟
- **缓存统计**：命中率、错误次数、响应时间

### 故障降级机制
1. **Redis连接失败**：自动降级到直接数据库查询
2. **缓存数据损坏**：自动重新从数据库加载
3. **熔断器机制**：连续失败时暂时禁用缓存

### 监控指标
- 缓存命中率（目标：>70%）
- API响应时间
- 数据库连接状态
- 系统资源使用情况

## 开发工作流

### 标准流程
1. **开发**：修改代码
2. **测试**：`go test ./...`
3. **质量检查**：`make lint`
4. **编译**：`make quick`
5. **性能测试**：`./scripts/benchmark.sh`
6. **清理**：`./clean.sh`
7. **发布**：`make all`

### 最佳实践
- 使用`make dev`进行开发模式编译（包含race检测）
- 定期运行性能测试和代码质量检查
- 监控缓存命中率和系统资源使用
- 使用健康检查API进行服务监控

## 安全和生产环境

### 生产环境配置
- 修改`runmode = prod`
- 配置SSL/TLS证书
- 使用标准邮件端口（25, 993, 995）
- 配置防火墙规则
- 设置Redis密码和网络访问控制

### DNS配置详解

#### 必需的DNS记录

1. **MX记录（邮件交换记录）**
   ```
   # 主域名的MX记录
   yourdomain.com. IN MX 10 mail.yourdomain.com.

   # 如果需要支持子域名邮件
   *.yourdomain.com. IN MX 10 mail.yourdomain.com.
   ```

2. **A记录（主机记录）**
   ```
   # 邮件服务器主机记录
   mail.yourdomain.com. IN A 你的服务器IP地址

   # 主域名记录（可选，用于Web界面）
   yourdomain.com. IN A 你的服务器IP地址
   ```

3. **SPF记录（防止邮件伪造）**
   ```
   # TXT记录格式
   yourdomain.com. IN TXT "v=spf1 ip4:你的服务器IP ~all"

   # 示例
   yourdomain.com. IN TXT "v=spf1 ip4:************* ~all"
   ```

#### 各大DNS服务商配置示例

##### 1. Cloudflare配置
```
记录类型: MX
名称: @
内容: mail.yourdomain.com
优先级: 10
TTL: 自动

记录类型: A
名称: mail
内容: 你的服务器IP
TTL: 自动

记录类型: TXT
名称: @
内容: v=spf1 ip4:你的服务器IP ~all
TTL: 自动
```

##### 2. 阿里云DNS配置
```
记录类型: MX
主机记录: @
记录值: mail.yourdomain.com
MX优先级: 10
TTL: 600

记录类型: A
主机记录: mail
记录值: 你的服务器IP
TTL: 600

记录类型: TXT
主机记录: @
记录值: v=spf1 ip4:你的服务器IP ~all
TTL: 600
```

##### 3. 腾讯云DNSPod配置
```
记录类型: MX
主机记录: @
记录值: mail.yourdomain.com.
MX优先级: 10
TTL: 600

记录类型: A
主机记录: mail
记录值: 你的服务器IP
TTL: 600

记录类型: TXT
主机记录: @
记录值: "v=spf1 ip4:你的服务器IP ~all"
TTL: 600
```

##### 4. GoDaddy配置
```
Type: MX
Host: @
Points to: mail.yourdomain.com
Priority: 10
TTL: 1 Hour

Type: A
Host: mail
Points to: 你的服务器IP
TTL: 1 Hour

Type: TXT
Host: @
TXT Value: v=spf1 ip4:你的服务器IP ~all
TTL: 1 Hour
```

#### DNS配置验证

1. **验证MX记录**
   ```bash
   # Linux/macOS
   dig MX yourdomain.com
   nslookup -type=MX yourdomain.com

   # Windows
   nslookup -type=MX yourdomain.com
   ```

2. **验证A记录**
   ```bash
   # 验证邮件服务器解析
   dig A mail.yourdomain.com
   nslookup mail.yourdomain.com
   ```

3. **验证SPF记录**
   ```bash
   # 查看TXT记录
   dig TXT yourdomain.com
   nslookup -type=TXT yourdomain.com
   ```

4. **在线验证工具**
   - MX Toolbox: https://mxtoolbox.com/
   - DNS Checker: https://dnschecker.org/
   - SPF Record Checker: https://www.kitterman.com/spf/validate.html

#### 高级DNS配置（可选）

1. **DKIM记录（邮件签名验证）**
   ```
   # 如果需要DKIM支持
   default._domainkey.yourdomain.com. IN TXT "v=DKIM1; k=rsa; p=你的公钥"
   ```

2. **DMARC记录（邮件策略）**
   ```
   _dmarc.yourdomain.com. IN TXT "v=DMARC1; p=quarantine; rua=mailto:<EMAIL>"
   ```

3. **反向DNS（PTR记录）**
   ```bash
   # 联系服务器提供商设置反向DNS
   # PTR记录应该指向 mail.yourdomain.com
   ```

#### 常见问题和解决方案

1. **DNS传播时间**
   - 通常需要2-4小时完全生效
   - 可以降低TTL值加快传播（如设置为300秒）

2. **MX记录优先级**
   - 数字越小优先级越高
   - 建议使用10作为主邮件服务器优先级

3. **通配符支持**
   ```
   # 支持所有子域名邮件
   *.yourdomain.com. IN MX 10 mail.yourdomain.com.
   ```

4. **多服务器负载均衡**
   ```
   yourdomain.com. IN MX 10 mail1.yourdomain.com.
   yourdomain.com. IN MX 20 mail2.yourdomain.com.
   ```

#### DNS配置检查清单

- [ ] MX记录指向正确的邮件服务器
- [ ] A记录解析到正确的服务器IP
- [ ] SPF记录配置正确
- [ ] DNS记录已生效（使用dig/nslookup验证）
- [ ] 防火墙开放相应端口（25, 2525, 1143, 1110）
- [ ] 服务器邮件服务正常运行

### 安全建议
- 实施速率限制
- 配置邮件大小限制
- 定期清理旧邮件
- 监控系统资源使用
- 配置日志轮转和备份

## 扩展和维护

### 添加新功能
1. 在`models/`中定义数据模型
2. 在`services/`中实现业务逻辑
3. 在`controllers/`中添加API接口
4. 在`routers/`中配置路由
5. 添加相应的测试用例

### 维护任务
- 定期更新依赖包
- 监控和优化缓存策略
- 清理过期邮件数据
- 备份重要配置和数据
- 更新文档和部署脚本

## 详细技术实现

### 数据库模型
```go
// Email 邮件模型
type Email struct {
    Id          int64     `orm:"auto"`
    MessageId   string    `orm:"size(255);unique"`
    ToAddress   string    `orm:"size(255);index"`
    FromAddress string    `orm:"size(255)"`
    Subject     string    `orm:"type(text)"`
    BodyText    string    `orm:"type(text)"`
    BodyHtml    string    `orm:"type(text)"`
    RawContent  string    `orm:"type(longtext)"`
    ReceivedAt  time.Time `orm:"auto_now_add;index"`
    Size        int       `orm:"default(0)"`
    Attachments []*Attachment `orm:"reverse(many)"`
}

// Attachment 附件模型
type Attachment struct {
    Id          int64  `orm:"auto"`
    Email       *Email `orm:"rel(fk)"`
    Filename    string `orm:"size(255)"`
    ContentType string `orm:"size(100)"`
    Size        int    `orm:"default(0)"`
    Content     string `orm:"type(longtext)"` // 存储base64编码的内容
}

// SetContentBytes 设置附件内容（将字节数组编码为base64字符串）
func (a *Attachment) SetContentBytes(content []byte) {
    a.Content = base64.StdEncoding.EncodeToString(content)
    a.Size = len(content)
}

// GetContentBytes 获取附件内容（将base64字符串解码为字节数组）
func (a *Attachment) GetContentBytes() ([]byte, error) {
    if a.Content == "" {
        return []byte{}, nil
    }
    return base64.StdEncoding.DecodeString(a.Content)
}
```

### 缓存键设计
```go
// 缓存键生成器
type CacheKeyGenerator struct{}

func (g *CacheKeyGenerator) EmailListKey(address string, page, limit int, includeContent bool) string {
    return fmt.Sprintf("email:list:%s:%d:%d:%t", address, page, limit, includeContent)
}

func (g *CacheKeyGenerator) EmailDetailKey(address string, id int64) string {
    return fmt.Sprintf("email:detail:%s:%d", address, id)
}

func (g *CacheKeyGenerator) EmailStatsKey(address string) string {
    return fmt.Sprintf("email:stats:%s", address)
}
```

### 性能优化技术
1. **数据库查询优化**
   - 使用索引：to_address, received_at, message_id
   - 分页查询排除大字段
   - 连接池配置优化

2. **缓存策略优化**
   - 多层缓存：列表缓存 + 详情缓存
   - 智能失效：新邮件到达时清除相关缓存
   - 预热机制：系统启动时预热热门邮箱

3. **并发安全优化**
   - Redis服务单例模式（sync.Once）
   - 统计信息读写锁保护
   - 优雅关闭机制

### 错误处理机制
```go
// 统一错误响应
type Response struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// 增强的错误处理
func (c *BaseController) ErrorResponse(code int, message string) {
    if code >= 500 {
        _, file, line, ok := runtime.Caller(1)
        if ok {
            log.Printf("服务器错误 [%d]: %s (位置: %s:%d)", code, message, file, line)
        }
    }
    // ... 响应处理
}
```

## 邮件客户端配置详解

#### 🔑 **密码机制说明**
本系统采用**无需注册**设计，密码验证规则：
- **用户名**：必须是有效邮箱格式（如：`<EMAIL>`）
- **密码**：可以是任意字符串，系统不验证密码内容
- **推荐密码**：`123456`、`password`、`temp` 或空密码

#### 📥 **IMAP配置（推荐用于邮件客户端）**
```
服务器: yourdomain.com (或服务器IP地址)
端口: 1143
用户名: 任意@yourdomain.com邮箱地址
密码: 任意字符串（推荐：123456）
加密: 无（关闭SSL/STARTTLS）
认证方式: 普通密码
```

#### 📥 **POP3配置（简单邮件下载）**
```
服务器: yourdomain.com (或服务器IP地址)
端口: 1110
用户名: 任意@yourdomain.com邮箱地址
密码: 任意字符串（推荐：123456）
加密: 无（关闭SSL/TLS）
认证方式: 普通密码
```

#### 📤 **SMTP发送配置**
```
服务器: yourdomain.com (或服务器IP地址)
端口: 2525
用户名: 任意@yourdomain.com邮箱地址（可选）
密码: 任意字符串（可选）
认证: 不需要（或使用任意用户名密码）
加密: 无
```

#### 💡 **使用示例**
```
假设要使用邮箱：<EMAIL>

配置参数：
- 服务器：mail.yourdomain.com
- IMAP端口：1143
- 用户名：<EMAIL>
- 密码：123456（或任意字符串）

配置完成后：
✅ 立即可以接收发送到 <EMAIL> 的邮件
✅ 无需注册或验证过程
✅ 支持多个不同的邮箱地址同时使用
```

#### ⚠️ **注意事项**
- 邮箱地址的域名部分必须匹配系统配置的域名
- 首次使用某个邮箱地址时，系统会自动创建
- 所有邮件都存储在数据库中，支持历史邮件查看
- 建议在邮件客户端中关闭SSL/TLS加密（开发环境）

## 缓存管理详解

### Redis配置优化
```ini
# Redis性能配置建议
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
tcp-keepalive 300
timeout 0
```

### 缓存监控指标
```json
{
  "cache_stats": {
    "hit_count": 1500,
    "miss_count": 300,
    "error_count": 5,
    "hit_rate": 83.33,
    "total_requests": 1805
  },
  "redis_info": {
    "status": "connected",
    "memory_usage": "45.2MB",
    "connected_clients": 8,
    "uptime": "2 days"
  }
}
```

### 缓存清理策略
```bash
# 定期清理脚本示例
#!/bin/bash
# 清理7天前的邮件缓存
redis-cli --scan --pattern "email:detail:*" | while read key; do
    ttl=$(redis-cli ttl "$key")
    if [ "$ttl" -lt 0 ]; then
        redis-cli del "$key"
    fi
done
```

## 部署和运维

### Docker Compose配置
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
      - "2525:2525"
      - "1143:1143"
      - "1110:1110"
    depends_on:
      - mysql
      - redis
    environment:
      - DB_HOST=mysql
      - REDIS_HOST=redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: temp_email
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 系统服务配置（systemd）
```ini
[Unit]
Description=Temporary Email System
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=temp-email
Group=temp-email
WorkingDirectory=/opt/goemail
ExecStart=/opt/goemail/goemail
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=GIN_MODE=release

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true

[Install]
WantedBy=multi-user.target
```

### 监控和告警
```bash
# 健康检查脚本
#!/bin/bash
HEALTH_URL="http://localhost:8080/health"
RESPONSE=$(curl -s -w "%{http_code}" "$HEALTH_URL")
HTTP_CODE="${RESPONSE: -3}"

if [ "$HTTP_CODE" != "200" ]; then
    echo "CRITICAL: Service health check failed (HTTP $HTTP_CODE)"
    # 发送告警通知
    exit 2
fi

# 检查缓存命中率
HIT_RATE=$(curl -s "$HEALTH_URL/metrics" | jq '.data.cache.hit_rate')
if (( $(echo "$HIT_RATE < 70" | bc -l) )); then
    echo "WARNING: Cache hit rate is low ($HIT_RATE%)"
    exit 1
fi

echo "OK: Service is healthy"
exit 0
```

## 故障排查指南

### 常见问题和解决方案

1. **邮件接收失败**
   ```bash
   # 检查SMTP服务状态
   netstat -tlnp | grep :2525

   # 查看SMTP日志
   journalctl -u goemail -f | grep SMTP

   # 测试SMTP连接
   telnet localhost 2525
   ```

2. **缓存性能问题**
   ```bash
   # 检查Redis连接
   redis-cli ping

   # 查看缓存统计
   curl http://localhost:8080/api/v1/cache/stats

   # 检查内存使用
   redis-cli info memory
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库连接
   mysql -h localhost -u root -p temp_email -e "SELECT 1"

   # 查看数据库状态
   curl http://localhost:8080/health
   ```

### 性能调优建议

1. **数据库优化**
   ```sql
   -- 添加索引
   CREATE INDEX idx_emails_to_received ON emails(to_address, received_at DESC);
   CREATE INDEX idx_emails_message_id ON emails(message_id);

   -- 定期清理旧数据
   DELETE FROM emails WHERE received_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
   ```

2. **Redis优化**
   ```bash
   # 监控慢查询
   redis-cli slowlog get 10

   # 优化内存使用
   redis-cli config set maxmemory-policy allkeys-lru
   ```

3. **应用优化**
   ```bash
   # 启用pprof性能分析
   go tool pprof http://localhost:8080/debug/pprof/profile

   # 内存分析
   go tool pprof http://localhost:8080/debug/pprof/heap
   ```

---

**最后更新**: 2024年 - 包含完整的技术实现细节和运维指南
