package controllers

import (
	"fmt"
	"strconv"
	"strings"

	"temp-email-system/models"
	"temp-email-system/services/email"
)

// EmailController 邮件控制器
type EmailController struct {
	BaseController
}

// EmailListResponse 邮件列表响应
type EmailListResponse struct {
	Emails []*models.Email `json:"emails"`
	Total  int64           `json:"total"`
	Page   int             `json:"page"`
	Limit  int             `json:"limit"`
}

// SendEmailRequest 发送邮件请求
type SendEmailRequest struct {
	To      string `json:"to"`
	Subject string `json:"subject"`
	Body    string `json:"body"`
	IsHtml  bool   `json:"is_html"`
}

// GetEmailsByAddress 获取指定邮箱地址的邮件列表或单封邮件详情
func (c *EmailController) GetEmailsByAddress() {
	address := c.Ctx.Input.Param(":address")
	emailId := c.Ctx.Input.Param(":id") // 可选的邮件ID参数

	if address == "" {
		c.BadRequestResponse("邮箱地址不能为空")
		return
	}

	// 验证邮箱地址格式
	if !c.validateEmailAddress(address) {
		c.BadRequestResponse("邮箱地址格式不正确")
		return
	}

	emailManager := models.NewEmailManager()

	// 如果提供了邮件ID，返回单封邮件详情
	if emailId != "" {
		id, err := strconv.ParseInt(emailId, 10, 64)
		if err != nil {
			c.ErrorResponse(400, "邮件ID格式不正确")
			return
		}

		// 获取邮件详情
		emailDetail, err := emailManager.GetEmailById(id)
		if err != nil {
			c.ErrorResponse(404, "邮件不存在")
			return
		}

		// 验证邮件是否属于指定地址
		if emailDetail.ToAddress != address {
			c.ErrorResponse(403, "无权访问此邮件")
			return
		}

		c.SuccessResponse(emailDetail)
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.GetString("page", "1"))
	limit, _ := strconv.Atoi(c.GetString("limit", "20"))
	includeContent := c.GetString("include_content", "false") == "true" // 是否包含邮件内容

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 查询邮件列表
	emailManager = models.NewEmailManager()

	var emails []*models.Email
	var total int64
	var err error

	if includeContent {
		// 包含完整内容的邮件列表
		emails, total, err = emailManager.GetEmailsWithContentByAddress(address, limit, offset)
	} else {
		// 只包含基本信息的邮件列表
		emails, total, err = emailManager.GetEmailsByAddress(address, limit, offset)
	}

	if err != nil {
		c.ErrorResponse(500, "查询邮件失败: "+err.Error())
		return
	}

	response := EmailListResponse{
		Emails: emails,
		Total:  total,
		Page:   page,
		Limit:  limit,
	}

	c.SuccessResponse(response)
}

// GetEmailDetail 获取邮件详情（保留此方法以兼容旧API）
func (c *EmailController) GetEmailDetail() {
	// 直接调用合并后的方法
	c.GetEmailsByAddress()
}

// SendEmail 发送邮件
func (c *EmailController) SendEmail() {
	var req SendEmailRequest
	if err := c.ParseJSON(&req); err != nil {
		c.ErrorResponse(400, "请求参数格式错误")
		return
	}
	
	// 验证必填字段
	if req.To == "" || req.Subject == "" || req.Body == "" {
		c.ErrorResponse(400, "收件人、主题和内容不能为空")
		return
	}
	
	// 发送邮件
	emailService := &email.EmailService{}
	err := emailService.SendEmail(req.To, req.Subject, req.Body, req.IsHtml)
	if err != nil {
		c.ErrorResponse(500, "发送邮件失败: "+err.Error())
		return
	}
	
	c.SuccessResponse(map[string]string{
		"message": "邮件发送成功",
	})
}

// GetAttachment 获取附件
func (c *EmailController) GetAttachment() {
	idStr := c.Ctx.Input.Param(":id")
	
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.ErrorResponse(400, "附件ID格式不正确")
		return
	}
	
	// 获取附件
	emailManager := models.NewEmailManager()
	attachment, err := emailManager.GetAttachment(id)
	if err != nil {
		c.ErrorResponse(404, "附件不存在")
		return
	}
	
	// 设置响应头
	c.Ctx.ResponseWriter.Header().Set("Content-Type", attachment.ContentType)
	c.Ctx.ResponseWriter.Header().Set("Content-Disposition", "attachment; filename=\""+attachment.Filename+"\"")
	c.Ctx.ResponseWriter.Header().Set("Content-Length", strconv.Itoa(len(attachment.Content)))
	
	// 输出附件内容
	c.Ctx.ResponseWriter.Write(attachment.Content)
}

// validateEmailAddress 验证邮箱地址格式
func (c *EmailController) validateEmailAddress(address string) bool {
	if address == "" {
		return false
	}

	// 基本格式检查
	if !strings.Contains(address, "@") {
		return false
	}

	parts := strings.Split(address, "@")
	if len(parts) != 2 {
		return false
	}

	// 检查用户名和域名部分
	username, domain := parts[0], parts[1]
	if username == "" || domain == "" {
		return false
	}

	// 检查域名格式
	if !strings.Contains(domain, ".") {
		return false
	}

	return true
}

// validatePaginationParams 验证分页参数
func (c *EmailController) validatePaginationParams(page, limit int) (int, int, error) {
	if page <= 0 {
		page = 1
	}

	if limit <= 0 {
		limit = 20
	} else if limit > 100 {
		return 0, 0, fmt.Errorf("每页数量不能超过100")
	}

	return page, limit, nil
}
