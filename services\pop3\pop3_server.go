package pop3

import (
	"bufio"
	"fmt"
	"log"
	"net"
	"strconv"
	"strings"

	"github.com/beego/beego/v2/server/web"
	"goemail/models"
	"goemail/services/email"
)

// POP3Server POP3服务器结构
type POP3Server struct {
	addr         string
	emailService *email.EmailService
}

// NewPOP3Server 创建POP3服务器
func NewPOP3Server(addr string) *POP3Server {
	return &POP3Server{
		addr:         addr,
		emailService: email.NewEmailService(),
	}
}

// POP3Connection POP3连接处理
type POP3Connection struct {
	conn         net.Conn
	reader       *bufio.Reader
	writer       *bufio.Writer
	emailService *email.EmailService
	username     string
	authenticated bool
	emails       []*models.Email
	deleted      map[int]bool
}

// NewPOP3Connection 创建POP3连接
func NewPOP3Connection(conn net.Conn, emailService *email.EmailService) *POP3Connection {
	return &POP3Connection{
		conn:         conn,
		reader:       bufio.NewReader(conn),
		writer:       bufio.NewWriter(conn),
		emailService: emailService,
		deleted:      make(map[int]bool),
	}
}

// Handle 处理POP3连接
func (c *POP3Connection) Handle() {
	defer c.conn.Close()

	// 发送欢迎消息
	c.writeResponse("+OK POP3 server ready")

	for {
		line, err := c.reader.ReadString('\n')
		if err != nil {
			log.Printf("POP3: Error reading from client: %v", err)
			break
		}

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		parts := strings.Fields(line)
		if len(parts) == 0 {
			continue
		}

		command := strings.ToUpper(parts[0])

		switch command {
		case "USER":
			c.handleUser(parts)
		case "PASS":
			c.handlePass(parts)
		case "STAT":
			c.handleStat()
		case "LIST":
			c.handleList(parts)
		case "RETR":
			c.handleRetr(parts)
		case "DELE":
			c.handleDele(parts)
		case "NOOP":
			c.writeResponse("+OK")
		case "RSET":
			c.handleRset()
		case "QUIT":
			c.handleQuit()
			return
		case "TOP":
			c.handleTop(parts)
		case "UIDL":
			c.handleUidl(parts)
		default:
			c.writeResponse("-ERR Unknown command")
		}
	}
}

// writeResponse 写入响应
func (c *POP3Connection) writeResponse(response string) {
	c.writer.WriteString(response + "\r\n")
	c.writer.Flush()
}

// handleUser 处理USER命令
func (c *POP3Connection) handleUser(parts []string) {
	if len(parts) < 2 {
		c.writeResponse("-ERR Missing username")
		return
	}

	username := parts[1]
	if !c.emailService.ValidateEmailAddress(username) {
		c.writeResponse("-ERR Invalid email address")
		return
	}

	if !c.emailService.IsValidDomain(username) {
		c.writeResponse("-ERR Domain not supported")
		return
	}

	c.username = username
	c.writeResponse("+OK User accepted")
}

// handlePass 处理PASS命令
func (c *POP3Connection) handlePass(parts []string) {
	if c.username == "" {
		c.writeResponse("-ERR Send USER first")
		return
	}

	// 临时邮箱不验证密码
	c.authenticated = true

	// 加载邮件
	err := c.loadEmails()
	if err != nil {
		c.writeResponse("-ERR Failed to load emails")
		return
	}

	c.writeResponse("+OK Mailbox ready")
}

// handleStat 处理STAT命令
func (c *POP3Connection) handleStat() {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	count := len(c.emails)
	size := 0
	for i, email := range c.emails {
		if !c.deleted[i+1] {
			size += email.Size
		}
	}

	c.writeResponse(fmt.Sprintf("+OK %d %d", count, size))
}

// handleList 处理LIST命令
func (c *POP3Connection) handleList(parts []string) {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	if len(parts) > 1 {
		// LIST specific message
		msgNum, err := strconv.Atoi(parts[1])
		if err != nil || msgNum < 1 || msgNum > len(c.emails) {
			c.writeResponse("-ERR Invalid message number")
			return
		}

		if c.deleted[msgNum] {
			c.writeResponse("-ERR Message deleted")
			return
		}

		email := c.emails[msgNum-1]
		c.writeResponse(fmt.Sprintf("+OK %d %d", msgNum, email.Size))
	} else {
		// LIST all messages
		c.writeResponse("+OK")
		for i, email := range c.emails {
			if !c.deleted[i+1] {
				c.writeResponse(fmt.Sprintf("%d %d", i+1, email.Size))
			}
		}
		c.writeResponse(".")
	}
}

// handleRetr 处理RETR命令
func (c *POP3Connection) handleRetr(parts []string) {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	if len(parts) < 2 {
		c.writeResponse("-ERR Missing message number")
		return
	}

	msgNum, err := strconv.Atoi(parts[1])
	if err != nil || msgNum < 1 || msgNum > len(c.emails) {
		c.writeResponse("-ERR Invalid message number")
		return
	}

	if c.deleted[msgNum] {
		c.writeResponse("-ERR Message deleted")
		return
	}

	email := c.emails[msgNum-1]

	// 构建邮件内容
	var content string
	if email.RawContent != "" {
		content = email.RawContent
	} else {
		content = fmt.Sprintf("From: %s\r\n", email.FromAddress)
		content += fmt.Sprintf("To: %s\r\n", email.ToAddress)
		content += fmt.Sprintf("Subject: %s\r\n", email.Subject)
		content += fmt.Sprintf("Date: %s\r\n", email.ReceivedAt.Format("Mon, 02 Jan 2006 15:04:05 -0700"))
		content += "Content-Type: text/plain; charset=utf-8\r\n"
		content += "\r\n"
		content += email.BodyText
	}

	c.writeResponse(fmt.Sprintf("+OK %d octets", len(content)))
	c.writeResponse(content)
	c.writeResponse(".")
}

// handleDele 处理DELE命令
func (c *POP3Connection) handleDele(parts []string) {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	if len(parts) < 2 {
		c.writeResponse("-ERR Missing message number")
		return
	}

	msgNum, err := strconv.Atoi(parts[1])
	if err != nil || msgNum < 1 || msgNum > len(c.emails) {
		c.writeResponse("-ERR Invalid message number")
		return
	}

	if c.deleted[msgNum] {
		c.writeResponse("-ERR Message already deleted")
		return
	}

	c.deleted[msgNum] = true
	c.writeResponse("+OK Message deleted")
}

// handleRset 处理RSET命令
func (c *POP3Connection) handleRset() {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	c.deleted = make(map[int]bool)
	c.writeResponse("+OK")
}

// handleQuit 处理QUIT命令
func (c *POP3Connection) handleQuit() {
	c.writeResponse("+OK Goodbye")
}

// handleTop 处理TOP命令
func (c *POP3Connection) handleTop(parts []string) {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	if len(parts) < 3 {
		c.writeResponse("-ERR Missing parameters")
		return
	}

	msgNum, err := strconv.Atoi(parts[1])
	if err != nil || msgNum < 1 || msgNum > len(c.emails) {
		c.writeResponse("-ERR Invalid message number")
		return
	}

	lines, err := strconv.Atoi(parts[2])
	if err != nil || lines < 0 {
		c.writeResponse("-ERR Invalid line count")
		return
	}

	if c.deleted[msgNum] {
		c.writeResponse("-ERR Message deleted")
		return
	}

	email := c.emails[msgNum-1]

	// 构建邮件头部
	header := fmt.Sprintf("From: %s\r\n", email.FromAddress)
	header += fmt.Sprintf("To: %s\r\n", email.ToAddress)
	header += fmt.Sprintf("Subject: %s\r\n", email.Subject)
	header += fmt.Sprintf("Date: %s\r\n", email.ReceivedAt.Format("Mon, 02 Jan 2006 15:04:05 -0700"))
	header += "Content-Type: text/plain; charset=utf-8"

	// 获取指定行数的正文
	bodyLines := strings.Split(email.BodyText, "\n")
	if lines > len(bodyLines) {
		lines = len(bodyLines)
	}

	content := header + "\r\n\r\n" + strings.Join(bodyLines[:lines], "\r\n")

	c.writeResponse("+OK")
	c.writeResponse(content)
	c.writeResponse(".")
}

// handleUidl 处理UIDL命令
func (c *POP3Connection) handleUidl(parts []string) {
	if !c.authenticated {
		c.writeResponse("-ERR Not authenticated")
		return
	}

	if len(parts) > 1 {
		// UIDL specific message
		msgNum, err := strconv.Atoi(parts[1])
		if err != nil || msgNum < 1 || msgNum > len(c.emails) {
			c.writeResponse("-ERR Invalid message number")
			return
		}

		if c.deleted[msgNum] {
			c.writeResponse("-ERR Message deleted")
			return
		}

		email := c.emails[msgNum-1]
		c.writeResponse(fmt.Sprintf("+OK %d %d", msgNum, email.Id))
	} else {
		// UIDL all messages
		c.writeResponse("+OK")
		for i, email := range c.emails {
			if !c.deleted[i+1] {
				c.writeResponse(fmt.Sprintf("%d %d", i+1, email.Id))
			}
		}
		c.writeResponse(".")
	}
}

// loadEmails 加载邮件列表
func (c *POP3Connection) loadEmails() error {
	emailManager := &models.EmailManager{}
	emails, _, err := emailManager.GetEmailsByAddress(c.username, 1000, 0)
	if err != nil {
		return err
	}

	c.emails = emails
	return nil
}

// StartPOP3Server 启动POP3服务器
func StartPOP3Server() error {
	// 获取POP3端口配置
	pop3Port, err := web.AppConfig.String("pop3_port")
	if err != nil {
		pop3Port = "1110" // 默认端口
	}

	addr := ":" + pop3Port
	log.Printf("Starting POP3 server on port %s", pop3Port)

	// 启动服务器
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %v", addr, err)
	}
	defer listener.Close()

	emailService := email.NewEmailService()

	for {
		conn, err := listener.Accept()
		if err != nil {
			log.Printf("POP3: Error accepting connection: %v", err)
			continue
		}

		// 为每个连接启动一个goroutine
		go func(conn net.Conn) {
			pop3Conn := NewPOP3Connection(conn, emailService)
			pop3Conn.Handle()
		}(conn)
	}
}
