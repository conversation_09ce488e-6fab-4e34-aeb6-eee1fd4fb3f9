package controllers

import (
	"strings"
	"time"

	"goemail/models"
	"goemail/services/cache"
)

// CacheController 缓存管理控制器
type CacheController struct {
	BaseController
}

// CacheStatsResponse 缓存统计响应
type CacheStatsResponse struct {
	Enabled    bool                `json:"enabled"`
	Stats      *cache.CacheStats   `json:"stats"`
	RedisInfo  map[string]string   `json:"redis_info,omitempty"`
}

// ClearCacheRequest 清除缓存请求
type ClearCacheRequest struct {
	Address string `json:"address"`
	Type    string `json:"type"` // list, detail, all
	EmailId int64  `json:"email_id,omitempty"`
}

// GetCacheStats 获取缓存统计信息
func (c *CacheController) GetCacheStats() {
	emailManager := models.NewEmailManager()
	redisService := cache.GetRedisService()
	
	response := CacheStatsResponse{
		Enabled: redisService.IsEnabled(),
		Stats:   emailManager.GetCacheStats(),
	}
	
	// 如果Redis启用，获取Redis信息
	if redisService.IsEnabled() {
		response.RedisInfo = map[string]string{
			"status": "connected",
		}
	} else {
		response.RedisInfo = map[string]string{
			"status": "disabled",
		}
	}
	
	c.SuccessResponse(response)
}

// ClearCache 清除缓存
func (c *CacheController) ClearCache() {
	var req ClearCacheRequest
	if err := c.ParseJSON(&req); err != nil {
		c.ErrorResponse(400, "请求参数格式错误")
		return
	}
	
	if req.Address == "" {
		c.ErrorResponse(400, "邮箱地址不能为空")
		return
	}
	
	// 验证邮箱地址格式
	if !strings.Contains(req.Address, "@") {
		c.ErrorResponse(400, "邮箱地址格式不正确")
		return
	}
	
	emailManager := models.NewEmailManager()
	var err error
	
	switch req.Type {
	case "list":
		// 清除邮件列表缓存
		keyGenerator := cache.NewCacheKeyGenerator()
		pattern := keyGenerator.EmailListPattern(req.Address)
		redisService := cache.GetRedisService()
		err = redisService.DeletePattern(pattern)
		
	case "detail":
		// 清除单封邮件详情缓存
		if req.EmailId <= 0 {
			c.ErrorResponse(400, "邮件ID不能为空")
			return
		}
		err = emailManager.ClearEmailDetailCache(req.Address, req.EmailId)
		
	case "all":
		// 清除所有相关缓存
		err = emailManager.ClearAllEmailCache(req.Address)
		
	default:
		c.ErrorResponse(400, "无效的缓存类型，支持: list, detail, all")
		return
	}
	
	if err != nil {
		c.ErrorResponse(500, "清除缓存失败: "+err.Error())
		return
	}
	
	c.SuccessResponse(map[string]string{
		"message": "缓存清除成功",
		"address": req.Address,
		"type":    req.Type,
	})
}

// WarmupCache 预热缓存
func (c *CacheController) WarmupCache() {
	address := c.GetString("address")
	if address == "" {
		c.ErrorResponse(400, "邮箱地址不能为空")
		return
	}
	
	// 验证邮箱地址格式
	if !strings.Contains(address, "@") {
		c.ErrorResponse(400, "邮箱地址格式不正确")
		return
	}
	
	emailManager := models.NewEmailManager()
	err := emailManager.WarmupCache(address)
	if err != nil {
		c.ErrorResponse(500, "缓存预热失败: "+err.Error())
		return
	}
	
	c.SuccessResponse(map[string]string{
		"message": "缓存预热成功",
		"address": address,
	})
}

// ResetCacheStats 重置缓存统计
func (c *CacheController) ResetCacheStats() {
	emailManager := models.NewEmailManager()
	emailManager.ResetCacheStats()
	
	c.SuccessResponse(map[string]string{
		"message": "缓存统计已重置",
	})
}

// FlushAllCache 清空所有缓存
func (c *CacheController) FlushAllCache() {
	redisService := cache.GetRedisService()
	
	if !redisService.IsEnabled() {
		c.ErrorResponse(400, "Redis缓存未启用")
		return
	}
	
	err := redisService.FlushAll()
	if err != nil {
		c.ErrorResponse(500, "清空缓存失败: "+err.Error())
		return
	}
	
	c.SuccessResponse(map[string]string{
		"message": "所有缓存已清空",
	})
}

// GetCacheKeys 获取缓存键列表
func (c *CacheController) GetCacheKeys() {
	pattern := c.GetString("pattern", "email:*")
	
	keyGenerator := cache.NewCacheKeyGenerator()
	keys := keyGenerator.GetCacheKeysByPattern(pattern)
	
	// 解析键信息
	keyInfos := make([]map[string]interface{}, len(keys))
	for i, key := range keys {
		keyInfos[i] = keyGenerator.GetKeyInfo(key)
		
		// 添加TTL信息
		redisService := cache.GetRedisService()
		if redisService.IsEnabled() {
			ttl := redisService.GetTTL(key)
			keyInfos[i]["ttl"] = ttl.Seconds()
		}
	}
	
	c.SuccessResponse(map[string]interface{}{
		"pattern": pattern,
		"count":   len(keys),
		"keys":    keyInfos,
	})
}

// GetCacheValue 获取缓存值
func (c *CacheController) GetCacheValue() {
	key := c.GetString("key")
	if key == "" {
		c.ErrorResponse(400, "缓存键不能为空")
		return
	}
	
	redisService := cache.GetRedisService()
	if !redisService.IsEnabled() {
		c.ErrorResponse(400, "Redis缓存未启用")
		return
	}
	
	// 验证键格式
	keyGenerator := cache.NewCacheKeyGenerator()
	if !keyGenerator.ValidateKey(key) {
		c.ErrorResponse(400, "无效的缓存键格式")
		return
	}
	
	var value interface{}
	err := redisService.Get(key, &value)
	if err != nil {
		c.ErrorResponse(404, "缓存不存在或已过期")
		return
	}
	
	keyInfo := keyGenerator.GetKeyInfo(key)
	ttl := redisService.GetTTL(key)
	
	c.SuccessResponse(map[string]interface{}{
		"key":   key,
		"value": value,
		"info":  keyInfo,
		"ttl":   ttl.Seconds(),
	})
}

// SetCacheValue 设置缓存值
func (c *CacheController) SetCacheValue() {
	var req struct {
		Key   string      `json:"key"`
		Value interface{} `json:"value"`
		TTL   int         `json:"ttl"` // 秒
	}
	
	if err := c.ParseJSON(&req); err != nil {
		c.ErrorResponse(400, "请求参数格式错误")
		return
	}
	
	if req.Key == "" {
		c.ErrorResponse(400, "缓存键不能为空")
		return
	}
	
	if req.TTL <= 0 {
		req.TTL = 300 // 默认5分钟
	}
	
	redisService := cache.GetRedisService()
	if !redisService.IsEnabled() {
		c.ErrorResponse(400, "Redis缓存未启用")
		return
	}
	
	// 验证键格式
	keyGenerator := cache.NewCacheKeyGenerator()
	if !keyGenerator.ValidateKey(req.Key) {
		c.ErrorResponse(400, "无效的缓存键格式")
		return
	}
	
	err := redisService.Set(req.Key, req.Value, time.Duration(req.TTL)*time.Second)
	if err != nil {
		c.ErrorResponse(500, "设置缓存失败: "+err.Error())
		return
	}
	
	c.SuccessResponse(map[string]interface{}{
		"message": "缓存设置成功",
		"key":     req.Key,
		"ttl":     req.TTL,
	})
}
