# Beego应用配置
appname = goemail
httpport = 8080
runmode = dev
autorender = true
copyrequestbody = true
EnableDocs = true

# 静态文件和模板配置
StaticDir = static:static
ViewsPath = views
EnableGzip = true

# 数据库配置
db_host = localhost
db_port = 3306
db_user = root
db_password = d933af3930d9bc45
db_name = temp_email
db_charset = utf8mb4

# 邮件服务配置
smtp_port = 465
imap_port = 993
pop3_port = 995
mail_domain = qali.cn

# Redis缓存配置
redis_enabled = true
redis_host = localhost
redis_port = 6379
redis_password =
redis_db = 0
redis_pool_size = 10
redis_min_idle_conns = 5

# 缓存配置
cache_email_list_ttl = 300
cache_email_detail_ttl = 1800
cache_email_stats_ttl = 600

# 日志配置
EnableAccessLogs = true
LogLevel = 7
