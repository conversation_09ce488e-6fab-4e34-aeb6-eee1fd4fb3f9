/* 邮箱管理页面样式 */

/* 邮箱生成器 */
.mailbox-generator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 0;
    color: white;
}

.generator-card {
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.generator-card h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 32px;
    color: white;
}

.generator-form {
    max-width: 600px;
    margin: 0 auto;
}

.input-group {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 12px;
    padding: 4px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.email-input {
    flex: 1;
    border: none;
    padding: 16px 20px;
    font-size: 16px;
    border-radius: 8px;
    outline: none;
    background-color: transparent;
}

.email-domain {
    padding: 16px 12px;
    color: #64748b;
    font-weight: 500;
    font-size: 16px;
}

.input-group .btn {
    margin: 0;
    border-radius: 8px;
    padding: 16px 24px;
    font-size: 16px;
}

.generated-email {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.email-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
}

.email-text {
    font-size: 18px;
    font-weight: 600;
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 邮件列表区域 */
.email-list-section {
    padding: 60px 0;
    background-color: #f8fafc;
}

.email-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32px;
    flex-wrap: wrap;
    gap: 20px;
}

.controls-left {
    display: flex;
    align-items: center;
    gap: 16px;
}

.controls-left h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
}

.email-count {
    background-color: #e0e7ff;
    color: #3730a3;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.controls-right {
    display: flex;
    align-items: center;
    gap: 12px;
}

.page-size-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 12px;
    background-color: white;
}

/* 邮件列表 */
.email-list {
    background-color: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.email-item {
    padding: 20px 24px;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.email-item:hover {
    background-color: #f8fafc;
}

.email-item:last-child {
    border-bottom: none;
}

.email-item.unread {
    background-color: #fef7ff;
    border-left: 4px solid #a855f7;
}

.email-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e0e7ff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #3730a3;
    font-size: 16px;
    flex-shrink: 0;
}

.email-info {
    flex: 1;
    min-width: 0;
}

.email-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.email-from {
    font-weight: 600;
    color: #1e293b;
    font-size: 14px;
}

.email-time {
    font-size: 12px;
    color: #64748b;
    flex-shrink: 0;
}

.email-subject {
    font-weight: 600;
    color: #374151;
    margin-bottom: 4px;
    font-size: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.email-preview {
    color: #64748b;
    font-size: 13px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.email-meta {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 8px;
}

.email-size {
    font-size: 11px;
    color: #64748b;
    background-color: #f1f5f9;
    padding: 2px 8px;
    border-radius: 12px;
}

.email-attachments-count {
    font-size: 11px;
    color: #059669;
    background-color: #ecfdf5;
    padding: 2px 8px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 80px 40px;
    color: #64748b;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.empty-state p {
    font-size: 14px;
    line-height: 1.6;
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
}

.page-info {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

/* 邮件详情模态框 */
.email-meta {
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 24px;
}

.meta-item {
    display: flex;
    margin-bottom: 12px;
}

.meta-item:last-child {
    margin-bottom: 0;
}

.meta-item label {
    font-weight: 600;
    color: #374151;
    width: 80px;
    flex-shrink: 0;
}

.meta-item span {
    color: #64748b;
    word-break: break-all;
}

.content-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 20px;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-btn.active {
    color: #2563eb;
    border-bottom-color: #2563eb;
}

.tab-btn:hover {
    color: #2563eb;
}

.content-panel {
    position: relative;
    min-height: 200px;
}

.tab-content {
    display: none;
    padding: 20px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    max-height: 400px;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
}

.tab-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    background: none;
    border: none;
    padding: 0;
}

/* 附件列表 */
.email-attachments {
    margin-top: 24px;
    padding-top: 24px;
    border-top: 1px solid #e5e7eb;
}

.email-attachments h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 16px;
}

.attachments-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.attachment-icon {
    font-size: 20px;
}

.attachment-details {
    display: flex;
    flex-direction: column;
}

.attachment-name {
    font-weight: 500;
    color: #374151;
    font-size: 14px;
}

.attachment-size {
    font-size: 12px;
    color: #64748b;
}

.attachment-download {
    padding: 6px 12px;
    font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .generator-card {
        padding: 24px;
    }
    
    .generator-card h2 {
        font-size: 24px;
    }
    
    .input-group {
        flex-direction: column;
        gap: 12px;
        padding: 16px;
    }
    
    .email-input,
    .email-domain {
        width: 100%;
        text-align: center;
    }
    
    .email-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .controls-right {
        justify-content: space-between;
    }
    
    .email-item {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .email-header {
        flex-direction: column;
        align-items: stretch;
        gap: 4px;
    }
    
    .email-time {
        text-align: left;
    }
}
