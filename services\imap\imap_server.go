package imap

import (
	"fmt"
	"log"
	"net"
	"strings"
	"time"

	"github.com/emersion/go-imap"
	"github.com/emersion/go-imap/backend"
	"github.com/emersion/go-imap/server"
	"github.com/beego/beego/v2/server/web"
	"temp-email-system/models"
	"temp-email-system/services/email"
)

// Backend IMAP后端实现
type Backend struct {
	emailService *email.EmailService
}

// NewBackend 创建IMAP后端
func NewBackend() *Backend {
	return &Backend{
		emailService: email.NewEmailService(),
	}
}

// Login 用户登录
func (be *Backend) Login(connInfo *imap.ConnInfo, username, password string) (backend.User, error) {
	// 临时邮箱系统，用户名就是邮箱地址
	if !be.emailService.ValidateEmailAddress(username) {
		return nil, fmt.Errorf("invalid email address")
	}
	
	if !be.emailService.IsValidDomain(username) {
		return nil, fmt.Errorf("domain not supported")
	}
	
	return &User{
		username: username,
		backend:  be,
	}, nil
}

// User IMAP用户实现
type User struct {
	username string
	backend  *Backend
}

// Username 获取用户名
func (u *User) Username() string {
	return u.username
}

// ListMailboxes 列出邮箱
func (u *User) ListMailboxes(subscribed bool) ([]backend.Mailbox, error) {
	// 临时邮箱只有一个INBOX
	return []backend.Mailbox{
		&Mailbox{
			name: "INBOX",
			user: u,
		},
	}, nil
}

// GetMailbox 获取邮箱
func (u *User) GetMailbox(name string) (backend.Mailbox, error) {
	if strings.ToUpper(name) != "INBOX" {
		return nil, fmt.Errorf("mailbox not found")
	}
	
	return &Mailbox{
		name: "INBOX",
		user: u,
	}, nil
}

// CreateMailbox 创建邮箱（不支持）
func (u *User) CreateMailbox(name string) error {
	return fmt.Errorf("creating mailboxes is not supported")
}

// DeleteMailbox 删除邮箱（不支持）
func (u *User) DeleteMailbox(name string) error {
	return fmt.Errorf("deleting mailboxes is not supported")
}

// RenameMailbox 重命名邮箱（不支持）
func (u *User) RenameMailbox(existingName, newName string) error {
	return fmt.Errorf("renaming mailboxes is not supported")
}

// Logout 登出
func (u *User) Logout() error {
	return nil
}

// Mailbox IMAP邮箱实现
type Mailbox struct {
	name string
	user *User
}

// Name 获取邮箱名称
func (mbox *Mailbox) Name() string {
	return mbox.name
}

// Info 获取邮箱信息
func (mbox *Mailbox) Info() (*imap.MailboxInfo, error) {
	info := &imap.MailboxInfo{
		Attributes: []string{},
		Delimiter:  "/",
		Name:       mbox.name,
	}

	return info, nil
}

// Status 获取邮箱状态
func (mbox *Mailbox) Status(items []imap.StatusItem) (*imap.MailboxStatus, error) {
	emailManager := models.NewEmailManager()
	_, total, err := emailManager.GetEmailsByAddress(mbox.user.username, 1, 0)
	if err != nil {
		return nil, err
	}

	status := &imap.MailboxStatus{
		Name:     mbox.name,
		Messages: uint32(total),
		Recent:   0,
		Unseen:   uint32(total), // 假设所有邮件都是未读的
		UidNext:  uint32(total + 1),
	}

	return status, nil
}

// SetSubscribed 设置订阅状态
func (mbox *Mailbox) SetSubscribed(subscribed bool) error {
	return nil // 不需要实现
}

// Check 检查邮箱
func (mbox *Mailbox) Check() error {
	return nil
}

// ListMessages 列出消息
func (mbox *Mailbox) ListMessages(uid bool, seqSet *imap.SeqSet, items []imap.FetchItem, ch chan<- *imap.Message) error {
	defer close(ch)

	emailManager := models.NewEmailManager()
	emails, _, err := emailManager.GetEmailsByAddress(mbox.user.username, 1000, 0)
	if err != nil {
		return err
	}
	
	for i, email := range emails {
		msg := &imap.Message{
			SeqNum: uint32(i + 1),
			Uid:    uint32(email.Id),
		}
		
		for _, item := range items {
			switch item {
			case imap.FetchEnvelope:
				fromParts := strings.Split(email.FromAddress, "@")
				toParts := strings.Split(email.ToAddress, "@")
				msg.Envelope = &imap.Envelope{
					Date:    email.ReceivedAt,
					Subject: email.Subject,
					From:    []*imap.Address{{PersonalName: "", MailboxName: fromParts[0], HostName: fromParts[1]}},
					To:      []*imap.Address{{PersonalName: "", MailboxName: toParts[0], HostName: toParts[1]}},
				}
			case imap.FetchBodyStructure:
				// 简化的body structure
				msg.BodyStructure = &imap.BodyStructure{
					MIMEType:    "text",
					MIMESubType: "plain",
				}
			case imap.FetchFlags:
				msg.Flags = []string{} // 没有标记
			case imap.FetchInternalDate:
				msg.InternalDate = email.ReceivedAt
			case imap.FetchRFC822Size:
				msg.Size = uint32(email.Size)
			}
		}
		
		ch <- msg
	}
	
	return nil
}

// SearchMessages 搜索消息
func (mbox *Mailbox) SearchMessages(uid bool, criteria *imap.SearchCriteria) ([]uint32, error) {
	// 简化实现，返回所有消息
	emailManager := models.NewEmailManager()
	emails, _, err := emailManager.GetEmailsByAddress(mbox.user.username, 1000, 0)
	if err != nil {
		return nil, err
	}
	
	var results []uint32
	for i := range emails {
		if uid {
			results = append(results, uint32(emails[i].Id))
		} else {
			results = append(results, uint32(i+1))
		}
	}
	
	return results, nil
}

// CreateMessage 创建消息（不支持）
func (mbox *Mailbox) CreateMessage(flags []string, date time.Time, body imap.Literal) error {
	return fmt.Errorf("creating messages is not supported")
}

// UpdateMessagesFlags 更新消息标记
func (mbox *Mailbox) UpdateMessagesFlags(uid bool, seqSet *imap.SeqSet, operation imap.FlagsOp, flags []string) error {
	// 临时邮箱不支持标记操作
	return nil
}

// CopyMessages 复制消息（不支持）
func (mbox *Mailbox) CopyMessages(uid bool, seqSet *imap.SeqSet, destName string) error {
	return fmt.Errorf("copying messages is not supported")
}

// Expunge 清除消息
func (mbox *Mailbox) Expunge() error {
	// 不支持删除操作
	return nil
}

// StartIMAPServer 启动IMAP服务器
func StartIMAPServer() error {
	// 获取IMAP端口配置
	imapPort, err := web.AppConfig.String("imap_port")
	if err != nil {
		imapPort = "1143" // 默认端口
	}
	
	// 创建IMAP服务器
	backend := NewBackend()
	s := server.New(backend)
	s.Addr = ":" + imapPort
	
	log.Printf("Starting IMAP server on port %s", imapPort)
	
	// 启动服务器
	listener, err := net.Listen("tcp", s.Addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %v", s.Addr, err)
	}
	
	return s.Serve(listener)
}
