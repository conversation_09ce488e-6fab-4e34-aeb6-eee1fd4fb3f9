/* 首页专用样式 */

/* 英雄区域 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-actions .btn {
    padding: 16px 32px;
    font-size: 16px;
    font-weight: 600;
    min-width: 160px;
}

.hero-actions .btn-primary {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.hero-actions .btn-primary:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

.hero-actions .btn-secondary {
    background-color: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.hero-actions .btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: white;
}

/* 功能特性区域 */
.features-section {
    padding: 80px 0;
    background-color: #f8fafc;
}

.section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 60px;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.feature-card {
    background-color: white;
    padding: 32px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid #f1f5f9;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

.feature-card h4 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
}

.feature-card p {
    color: #64748b;
    line-height: 1.6;
}

/* 快速开始区域 */
.quickstart-section {
    padding: 80px 0;
    background-color: white;
}

.quickstart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.quickstart-card {
    background-color: #f8fafc;
    padding: 32px;
    border-radius: 16px;
    text-align: center;
    border: 2px solid #e2e8f0;
    position: relative;
    transition: all 0.3s ease;
}

.quickstart-card:hover {
    border-color: #2563eb;
    transform: translateY(-4px);
}

.step-number {
    position: absolute;
    top: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 32px;
    height: 32px;
    background-color: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 16px;
}

.quickstart-card h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 20px 0 12px;
}

.quickstart-card p {
    color: #64748b;
    margin-bottom: 16px;
    line-height: 1.6;
}

.example {
    background-color: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    color: #059669;
    font-weight: 500;
}

/* 系统状态区域 */
.status-section {
    padding: 80px 0;
    background-color: #f8fafc;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-top: 40px;
}

.status-card {
    background-color: white;
    padding: 24px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.status-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-icon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
}

.status-card h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.status-value {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.status-value.online {
    color: #059669;
}

.status-value.offline {
    color: #dc2626;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 32px;
    }
    
    .hero-description {
        font-size: 16px;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 280px;
    }
    
    .section-title {
        font-size: 28px;
    }
    
    .features-grid,
    .quickstart-grid,
    .status-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .feature-card,
    .quickstart-card,
    .status-card {
        padding: 24px;
    }
}
