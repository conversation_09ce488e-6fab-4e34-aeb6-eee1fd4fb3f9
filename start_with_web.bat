@echo off
chcp 65001 >nul

REM 临时邮箱系统启动脚本（包含Web界面）

echo 🚀 启动临时邮箱系统...
echo ================================

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go环境未安装，请先安装Go语言环境
    pause
    exit /b 1
)

echo ✅ Go环境检查通过

REM 设置Go代理（解决网络问题）
echo 🔧 配置Go代理...
set GOPROXY=https://goproxy.cn,direct
set GOSUMDB=sum.golang.google.cn

REM 下载依赖
echo 📦 下载Go模块依赖...
go mod tidy

if %errorlevel% neq 0 (
    echo ❌ 依赖下载失败，请检查网络连接
    pause
    exit /b 1
)

echo ✅ 依赖下载完成

REM 编译项目
echo 🔨 编译项目...
go build -o temp-email-system.exe .

if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查代码
    pause
    exit /b 1
)

echo ✅ 编译完成

REM 启动系统
echo 🌟 启动临时邮箱系统...
echo ================================
echo 📧 Web界面: http://localhost:8080
echo 📧 邮箱管理: http://localhost:8080/mailbox
echo 📧 API文档: http://localhost:8080/api
echo 📧 配置说明: http://localhost:8080/config
echo ================================
echo 🔧 SMTP端口: 2525
echo 🔧 IMAP端口: 1143
echo 🔧 POP3端口: 1110
echo ================================
echo 按 Ctrl+C 停止服务
echo.

REM 运行系统
temp-email-system.exe

pause
