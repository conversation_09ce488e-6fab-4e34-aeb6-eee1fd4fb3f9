#!/bin/bash

echo "=== 临时邮箱系统启动脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go语言环境"
    exit 1
fi

# 检查MySQL是否运行
if ! command -v mysql &> /dev/null; then
    echo "⚠️  MySQL客户端未找到，请确保MySQL服务器正在运行"
fi

echo "📦 安装依赖包..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败"
    exit 1
fi

echo "🔧 检查配置文件..."
if [ ! -f "conf/app.conf" ]; then
    echo "❌ 配置文件不存在: conf/app.conf"
    exit 1
fi

echo "🚀 启动临时邮箱系统..."
echo ""
echo "服务端口："
echo "  - Web API: http://localhost:8080"
echo "  - SMTP:    localhost:2525"
echo "  - IMAP:    localhost:1143"
echo "  - POP3:    localhost:1110"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动应用
go run main.go
