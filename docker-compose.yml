version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: temp-email-mysql
    environment:
      MYSQL_ROOT_PASSWORD: temp_email_123
      MYSQL_DATABASE: temp_email
      MYSQL_USER: temp_email_user
      MY<PERSON><PERSON>_PASSWORD: temp_email_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - temp-email-network

  redis:
    image: redis:7-alpine
    container_name: temp-email-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - temp-email-network

  temp-email-app:
    build: .
    container_name: temp-email-app
    depends_on:
      - mysql
      - redis
    ports:
      - "8080:8080"   # Web API
      - "465:465"   # SMTP
      - "993:993"   # IMAP
      - "995:995"   # POP3
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=temp_email_user
      - DB_PASSWORD=temp_email_pass
      - DB_NAME=temp_email
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./conf:/app/conf
    networks:
      - temp-email-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  temp-email-network:
    driver: bridge
