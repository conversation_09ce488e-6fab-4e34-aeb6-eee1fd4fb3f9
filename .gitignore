# 编译输出
*.exe
*.out
goemail
goemail.exe

# 构建目录
build/
dist/
release/

# 发布包
*.zip
*.tar.gz
*.tar.bz2

# Go相关
*.so
*.dylib
*.test
*.prof
go.work
go.work.sum

# 依赖目录
vendor/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 配置文件（可能包含敏感信息）
conf/app.conf.local
conf/app.conf.prod
.env
.env.local

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存
*.cache

# 测试覆盖率
coverage.txt
coverage.html
*.cover

# 文档生成
docs/_build/

# Node.js（如果有前端）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python（如果有Python脚本）
__pycache__/
*.py[cod]
*$py.class
*.egg-info/
.pytest_cache/

# 性能分析文件
*.pprof
cpu.prof
mem.prof
block.prof
mutex.prof

# 调试文件
debug
*.debug

# 脚本生成的文件
scripts/*.exe
scripts/temp-*

# 其他
*.orig
.vscode/settings.json

# 代码质量检查报告
.golangci-lint-cache/
lint-report.xml
coverage-report.xml
