<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web界面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🧪 Web界面功能测试</h1>
    
    <div class="test-card">
        <h2>📧 临时邮箱系统Web界面</h2>
        <p>点击下面的按钮测试各个页面：</p>
        
        <button class="btn" onclick="testPage('/')">测试首页</button>
        <button class="btn" onclick="testPage('/mailbox')">测试邮箱管理</button>
        <button class="btn" onclick="testPage('/api')">测试API文档</button>
        <button class="btn" onclick="testPage('/config')">测试配置说明</button>
        
        <div id="testResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-card">
        <h2>🔧 API接口测试</h2>
        <p>测试后端API接口：</p>
        
        <button class="btn" onclick="testAPI('/health')">健康检查</button>
        <button class="btn" onclick="testAPI('/system/info')">系统信息</button>
        <button class="btn" onclick="testAPI('/api/v1/cache/stats')">缓存统计</button>
        
        <div id="apiResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-card">
        <h2>📋 创建的文件列表</h2>
        <ul>
            <li>✅ <strong>controllers/web.go</strong> - Web界面控制器</li>
            <li>✅ <strong>views/index.html</strong> - 首页模板</li>
            <li>✅ <strong>views/mailbox.html</strong> - 邮箱管理页面</li>
            <li>✅ <strong>views/api.html</strong> - API文档页面</li>
            <li>✅ <strong>views/config.html</strong> - 配置说明页面</li>
            <li>✅ <strong>static/css/style.css</strong> - 全局样式</li>
            <li>✅ <strong>static/css/index.css</strong> - 首页样式</li>
            <li>✅ <strong>static/css/mailbox.css</strong> - 邮箱页面样式</li>
            <li>✅ <strong>static/css/api.css</strong> - API文档样式</li>
            <li>✅ <strong>static/css/config.css</strong> - 配置页面样式</li>
            <li>✅ <strong>static/js/common.js</strong> - 通用JavaScript</li>
            <li>✅ <strong>static/js/index.js</strong> - 首页JavaScript</li>
            <li>✅ <strong>static/js/mailbox.js</strong> - 邮箱管理JavaScript</li>
            <li>✅ <strong>static/js/api.js</strong> - API文档JavaScript</li>
            <li>✅ <strong>static/js/config.js</strong> - 配置页面JavaScript</li>
        </ul>
    </div>
    
    <div class="test-card">
        <h2>🚀 启动说明</h2>
        <ol>
            <li>确保MySQL和Redis服务正在运行</li>
            <li>运行 <code>go run .</code> 启动系统</li>
            <li>访问 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
            <li>开始使用Web界面！</li>
        </ol>
    </div>

    <script>
        async function testPage(url) {
            const resultDiv = document.getElementById('testResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch(url);
                const text = await response.text();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ 页面 ${url} 测试成功！\n状态码: ${response.status}\n内容长度: ${text.length} 字符`;
                    
                    // 在新窗口打开页面
                    window.open(url, '_blank');
                } else {
                    resultDiv.textContent = `❌ 页面 ${url} 测试失败！\n状态码: ${response.status}\n错误: ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ 页面 ${url} 测试失败！\n错误: ${error.message}`;
            }
        }
        
        async function testAPI(url) {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '测试中...';
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ API ${url} 测试成功！\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.textContent = `❌ API ${url} 测试失败！\n状态码: ${response.status}\n错误: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.textContent = `❌ API ${url} 测试失败！\n错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
