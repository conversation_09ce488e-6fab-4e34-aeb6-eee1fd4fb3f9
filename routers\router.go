package routers

import (
	"github.com/beego/beego/v2/server/web"
	"goemail/controllers"
)

func init() {
	// API路由
	ns := web.NewNamespace("/api/v1",
		// 邮件相关API
		web.NSNamespace("/emails",
			// 合并的邮件API：支持获取列表和单封邮件详情
			web.NSRouter("/:address", &controllers.EmailController{}, "get:GetEmailsByAddress"),
			web.NSRouter("/:address/:id", &controllers.EmailController{}, "get:GetEmailsByAddress"), // 使用同一个方法
			web.NSRouter("/send", &controllers.EmailController{}, "post:SendEmail"),
		),
		// 附件相关API
		web.NSNamespace("/attachments",
			web.NSRouter("/:id", &controllers.EmailController{}, "get:GetAttachment"),
		),
		// 缓存管理API
		web.NSNamespace("/cache",
			web.NSRouter("/stats", &controllers.CacheController{}, "get:GetCacheStats"),
			web.NSRouter("/clear", &controllers.CacheController{}, "post:ClearCache"),
			web.NSRouter("/warmup", &controllers.CacheController{}, "post:WarmupCache"),
			web.NSRouter("/reset-stats", &controllers.CacheController{}, "post:ResetCacheStats"),
			web.NSRouter("/flush-all", &controllers.CacheController{}, "post:FlushAllCache"),
			web.NSRouter("/keys", &controllers.CacheController{}, "get:GetCacheKeys"),
			web.NSRouter("/value", &controllers.CacheController{}, "get:GetCacheValue;post:SetCacheValue"),
		),
	)

	web.AddNamespace(ns)

	// 健康检查路由
	web.Router("/health", &controllers.HealthController{}, "get:Check")
	web.Router("/health/metrics", &controllers.HealthController{}, "get:Metrics")
	web.Router("/health/ready", &controllers.HealthController{}, "get:Ready")
	web.Router("/health/live", &controllers.HealthController{}, "get:Live")

	// Web界面路由
	web.Router("/", &controllers.WebController{}, "get:Index")
	web.Router("/api", &controllers.WebController{}, "get:API")
	web.Router("/mailbox", &controllers.WebController{}, "get:Mailbox")
	web.Router("/config", &controllers.WebController{}, "get:Config")
	web.Router("/system/info", &controllers.WebController{}, "get:GetSystemInfo")

	// 兼容性路由
	web.Router("/status", &controllers.BaseController{}, "get:Health")
}
