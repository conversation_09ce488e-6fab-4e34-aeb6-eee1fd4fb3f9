<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱管理 - 临时邮箱系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/mailbox.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>📧 临时邮箱系统</h1>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">首页</a>
                <a href="/mailbox" class="nav-link active">邮箱管理</a>
                <a href="/api" class="nav-link">API文档</a>
                <a href="/config" class="nav-link">配置说明</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 邮箱生成器 -->
        <section class="mailbox-generator">
            <div class="container">
                <div class="generator-card">
                    <h2>创建临时邮箱</h2>
                    <div class="generator-form">
                        <div class="input-group">
                            <input type="text" id="emailPrefix" placeholder="输入邮箱前缀" class="email-input">
                            <span class="email-domain">@<span id="domainName">yourdomain.com</span></span>
                            <button id="generateBtn" class="btn btn-primary">生成邮箱</button>
                        </div>
                        <div class="generated-email" id="generatedEmail" style="display: none;">
                            <div class="email-display">
                                <span class="email-text" id="emailText"></span>
                                <button id="copyBtn" class="btn btn-copy">复制</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 邮件列表 -->
        <section class="email-list-section">
            <div class="container">
                <div class="email-controls">
                    <div class="controls-left">
                        <h3>邮件列表</h3>
                        <span class="email-count" id="emailCount">0 封邮件</span>
                    </div>
                    <div class="controls-right">
                        <button id="refreshBtn" class="btn btn-secondary">🔄 刷新</button>
                        <button id="autoRefreshBtn" class="btn btn-secondary">⏱️ 自动刷新</button>
                        <select id="pageSize" class="page-size-select">
                            <option value="10">10条/页</option>
                            <option value="20" selected>20条/页</option>
                            <option value="50">50条/页</option>
                        </select>
                    </div>
                </div>

                <!-- 邮件列表容器 -->
                <div class="email-list" id="emailList">
                    <div class="empty-state" id="emptyState">
                        <div class="empty-icon">📭</div>
                        <h4>暂无邮件</h4>
                        <p>请先生成邮箱地址，然后等待邮件到达</p>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="pagination" id="pagination" style="display: none;">
                    <button id="prevPage" class="btn btn-page">上一页</button>
                    <span class="page-info" id="pageInfo">第 1 页，共 1 页</span>
                    <button id="nextPage" class="btn btn-page">下一页</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 邮件详情模态框 -->
    <div class="modal" id="emailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="emailSubject">邮件详情</h3>
                <button class="modal-close" id="closeModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="email-meta">
                    <div class="meta-item">
                        <label>发件人:</label>
                        <span id="emailFrom"></span>
                    </div>
                    <div class="meta-item">
                        <label>收件人:</label>
                        <span id="emailTo"></span>
                    </div>
                    <div class="meta-item">
                        <label>时间:</label>
                        <span id="emailTime"></span>
                    </div>
                    <div class="meta-item">
                        <label>大小:</label>
                        <span id="emailSize"></span>
                    </div>
                </div>
                <div class="email-content">
                    <div class="content-tabs">
                        <button class="tab-btn active" data-tab="text">纯文本</button>
                        <button class="tab-btn" data-tab="html">HTML</button>
                        <button class="tab-btn" data-tab="raw">原始内容</button>
                    </div>
                    <div class="content-panel">
                        <div class="tab-content active" id="textContent"></div>
                        <div class="tab-content" id="htmlContent"></div>
                        <div class="tab-content" id="rawContent"></div>
                    </div>
                </div>
                <div class="email-attachments" id="emailAttachments" style="display: none;">
                    <h4>附件</h4>
                    <div class="attachments-list" id="attachmentsList"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading" id="loading" style="display: none;">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
    </div>

    <!-- 通知消息 -->
    <div class="notification" id="notification"></div>

    <script src="/static/js/common.js"></script>
    <script src="/static/js/mailbox.js"></script>
</body>
</html>
