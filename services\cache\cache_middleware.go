package cache

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/beego/beego/v2/server/web"
	beegoContext "github.com/beego/beego/v2/server/web/context"
)

// CacheMiddleware 缓存中间件
type CacheMiddleware struct {
	redisService *RedisService
}

// NewCacheMiddleware 创建缓存中间件
func NewCacheMiddleware() *CacheMiddleware {
	return &CacheMiddleware{
		redisService: GetRedisService(),
	}
}

// CacheMonitorFilter 缓存监控过滤器
func CacheMonitorFilter(ctx *beegoContext.Context) {
	// 记录请求开始时间
	startTime := time.Now()
	
	// 在请求头中添加缓存状态信息
	redisService := GetRedisService()
	if redisService.IsEnabled() {
		ctx.ResponseWriter.Header().Set("X-Cache-Enabled", "true")
	} else {
		ctx.ResponseWriter.Header().Set("X-Cache-Enabled", "false")
	}
	
	// 记录请求处理时间
	defer func() {
		duration := time.Since(startTime)
		log.Printf("请求处理时间: %v, 路径: %s", duration, ctx.Request.URL.Path)
		
		// 添加响应时间头
		ctx.ResponseWriter.Header().Set("X-Response-Time", duration.String())
	}()
}

// CacheHealthCheck 缓存健康检查
func CacheHealthCheck() map[string]interface{} {
	redisService := GetRedisService()
	
	health := map[string]interface{}{
		"enabled": redisService.IsEnabled(),
		"status":  "unknown",
	}
	
	if !redisService.IsEnabled() {
		health["status"] = "disabled"
		health["message"] = "Redis缓存未启用"
		return health
	}
	
	// 测试Redis连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	_, err := redisService.client.Ping(ctx).Result()
	if err != nil {
		health["status"] = "error"
		health["message"] = "Redis连接失败: " + err.Error()
		log.Printf("Redis健康检查失败: %v", err)
	} else {
		health["status"] = "healthy"
		health["message"] = "Redis连接正常"
		
		// 获取Redis信息
		info, err := redisService.client.Info(ctx).Result()
		if err == nil {
			health["redis_info"] = parseRedisInfo(info)
		}
	}
	
	// 添加缓存统计信息
	stats := redisService.GetStats()
	health["stats"] = stats
	
	return health
}

// parseRedisInfo 解析Redis信息
func parseRedisInfo(info string) map[string]string {
	result := make(map[string]string)

	// 简单解析Redis INFO命令的输出
	lines := strings.Split(info, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, ":", 2)
		if len(parts) == 2 {
			result[parts[0]] = parts[1]
		}
	}

	// 返回一些关键信息
	return map[string]string{
		"version":     result["redis_version"],
		"used_memory": result["used_memory_human"],
		"connected":   "true",
	}
}

// CachePerformanceMonitor 缓存性能监控
type CachePerformanceMonitor struct {
	requestCount    int64
	cacheHitCount   int64
	cacheMissCount  int64
	errorCount      int64
	totalTime       time.Duration
	lastResetTime   time.Time
}

var performanceMonitor = &CachePerformanceMonitor{
	lastResetTime: time.Now(),
}

// RecordRequest 记录请求
func (m *CachePerformanceMonitor) RecordRequest(duration time.Duration, cacheHit bool, hasError bool) {
	m.requestCount++
	m.totalTime += duration
	
	if hasError {
		m.errorCount++
	} else if cacheHit {
		m.cacheHitCount++
	} else {
		m.cacheMissCount++
	}
}

// GetPerformanceStats 获取性能统计
func (m *CachePerformanceMonitor) GetPerformanceStats() map[string]interface{} {
	totalRequests := m.cacheHitCount + m.cacheMissCount
	var hitRate float64
	if totalRequests > 0 {
		hitRate = float64(m.cacheHitCount) / float64(totalRequests) * 100
	}
	
	var avgResponseTime float64
	if m.requestCount > 0 {
		avgResponseTime = float64(m.totalTime.Nanoseconds()) / float64(m.requestCount) / 1000000 // 转换为毫秒
	}
	
	return map[string]interface{}{
		"total_requests":     m.requestCount,
		"cache_hits":         m.cacheHitCount,
		"cache_misses":       m.cacheMissCount,
		"cache_hit_rate":     hitRate,
		"error_count":        m.errorCount,
		"avg_response_time":  avgResponseTime,
		"total_time":         m.totalTime.String(),
		"monitoring_since":   m.lastResetTime,
	}
}

// ResetPerformanceStats 重置性能统计
func (m *CachePerformanceMonitor) ResetPerformanceStats() {
	m.requestCount = 0
	m.cacheHitCount = 0
	m.cacheMissCount = 0
	m.errorCount = 0
	m.totalTime = 0
	m.lastResetTime = time.Now()
}

// GetGlobalPerformanceStats 获取全局性能统计
func GetGlobalPerformanceStats() map[string]interface{} {
	return performanceMonitor.GetPerformanceStats()
}

// ResetGlobalPerformanceStats 重置全局性能统计
func ResetGlobalPerformanceStats() {
	performanceMonitor.ResetPerformanceStats()
}

// CacheCircuitBreaker 缓存熔断器
type CacheCircuitBreaker struct {
	failureCount    int
	failureThreshold int
	resetTimeout    time.Duration
	lastFailureTime time.Time
	state          string // "closed", "open", "half-open"
}

var circuitBreaker = &CacheCircuitBreaker{
	failureThreshold: 5,
	resetTimeout:     30 * time.Second,
	state:           "closed",
}

// ShouldUseCache 判断是否应该使用缓存
func (cb *CacheCircuitBreaker) ShouldUseCache() bool {
	switch cb.state {
	case "open":
		// 检查是否可以进入半开状态
		if time.Since(cb.lastFailureTime) > cb.resetTimeout {
			cb.state = "half-open"
			log.Println("缓存熔断器进入半开状态")
			return true
		}
		return false
	case "half-open":
		return true
	default: // closed
		return true
	}
}

// RecordSuccess 记录成功
func (cb *CacheCircuitBreaker) RecordSuccess() {
	if cb.state == "half-open" {
		cb.state = "closed"
		cb.failureCount = 0
		log.Println("缓存熔断器恢复到关闭状态")
	}
}

// RecordFailure 记录失败
func (cb *CacheCircuitBreaker) RecordFailure() {
	cb.failureCount++
	cb.lastFailureTime = time.Now()
	
	if cb.failureCount >= cb.failureThreshold {
		cb.state = "open"
		log.Printf("缓存熔断器打开，失败次数: %d", cb.failureCount)
	}
}

// GetCircuitBreakerStatus 获取熔断器状态
func GetCircuitBreakerStatus() map[string]interface{} {
	return map[string]interface{}{
		"state":             circuitBreaker.state,
		"failure_count":     circuitBreaker.failureCount,
		"failure_threshold": circuitBreaker.failureThreshold,
		"last_failure_time": circuitBreaker.lastFailureTime,
		"reset_timeout":     circuitBreaker.resetTimeout.String(),
	}
}

// SafeCacheOperation 安全的缓存操作（带熔断器）
func SafeCacheOperation(operation func() error) error {
	if !circuitBreaker.ShouldUseCache() {
		return ErrCacheCircuitOpen
	}
	
	err := operation()
	if err != nil {
		circuitBreaker.RecordFailure()
		return err
	}
	
	circuitBreaker.RecordSuccess()
	return nil
}

// 自定义错误
var (
	ErrCacheCircuitOpen = fmt.Errorf("cache circuit breaker is open")
)

// 初始化缓存中间件
func init() {
	// 注册缓存监控过滤器
	web.InsertFilter("/*", web.BeforeRouter, CacheMonitorFilter)
}
