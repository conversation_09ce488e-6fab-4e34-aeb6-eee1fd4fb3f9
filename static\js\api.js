// API文档页面JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    initAPIPage();
    bindAPIEvents();
});

// 初始化API页面
function initAPIPage() {
    console.log('API文档页面初始化');
    
    // 设置默认显示的API接口
    showAPISection('get-emails');
    
    // 填充默认的测试数据
    fillDefaultTestData();
}

// 绑定API页面事件
function bindAPIEvents() {
    // 侧边栏导航点击事件
    const navItems = document.querySelectorAll('.api-nav .nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            showAPISection(targetId);
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 显示指定的API接口
function showAPISection(sectionId) {
    const sections = document.querySelectorAll('.api-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.style.display = 'block';
    }
}

// 填充默认测试数据
function fillDefaultTestData() {
    // 等待系统信息加载完成
    setTimeout(() => {
        const emailInputs = document.querySelectorAll('input[id*="emailAddress"]');
        emailInputs.forEach(input => {
            if (!input.value) {
                input.value = `test@${systemInfo.mail_domain}`;
            }
        });
    }, 1000);
}

// API测试函数

// 测试获取邮件列表
async function testGetEmails() {
    const emailAddress = document.getElementById('emailAddress1').value;
    const page = document.getElementById('page1').value || 1;
    const limit = document.getElementById('limit1').value || 20;
    const includeContent = document.getElementById('includeContent1').checked;
    const resultElement = document.getElementById('result1');
    
    if (!emailAddress) {
        Utils.showNotification('请输入邮箱地址', 'warning');
        return;
    }
    
    if (!Utils.validateEmail(emailAddress)) {
        Utils.showNotification('请输入有效的邮箱地址', 'error');
        return;
    }
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.getEmails(emailAddress, parseInt(page), parseInt(limit), includeContent);
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.code === 200) {
            Utils.showNotification('API调用成功', 'success');
        } else {
            Utils.showNotification(`API调用失败: ${response.message}`, 'error');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('API调用失败: ' + error.message, 'error');
    }
}

// 测试获取邮件详情
async function testGetEmailDetail() {
    const emailAddress = document.getElementById('emailAddress2').value;
    const emailId = document.getElementById('emailId2').value;
    const resultElement = document.getElementById('result2');
    
    if (!emailAddress || !emailId) {
        Utils.showNotification('请输入邮箱地址和邮件ID', 'warning');
        return;
    }
    
    if (!Utils.validateEmail(emailAddress)) {
        Utils.showNotification('请输入有效的邮箱地址', 'error');
        return;
    }
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.getEmailDetail(emailAddress, emailId);
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.code === 200) {
            Utils.showNotification('API调用成功', 'success');
        } else {
            Utils.showNotification(`API调用失败: ${response.message}`, 'error');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('API调用失败: ' + error.message, 'error');
    }
}

// 测试健康检查
async function testHealthCheck() {
    const resultElement = document.getElementById('result3');
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.healthCheck();
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.status === 'ok') {
            Utils.showNotification('健康检查通过', 'success');
        } else {
            Utils.showNotification('系统状态异常', 'warning');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('健康检查失败: ' + error.message, 'error');
    }
}

// 测试缓存统计
async function testCacheStats() {
    const resultElement = document.getElementById('result4');
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.getCacheStats();
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.code === 200) {
            Utils.showNotification('获取缓存统计成功', 'success');
        } else {
            Utils.showNotification(`获取缓存统计失败: ${response.message}`, 'error');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('获取缓存统计失败: ' + error.message, 'error');
    }
}

// 测试发送邮件
async function testSendEmail() {
    const from = document.getElementById('sendFrom').value;
    const to = document.getElementById('sendTo').value;
    const subject = document.getElementById('sendSubject').value;
    const content = document.getElementById('sendContent').value;
    const resultElement = document.getElementById('result5');
    
    if (!from || !to || !subject) {
        Utils.showNotification('请填写必需的字段', 'warning');
        return;
    }
    
    if (!Utils.validateEmail(from) || !Utils.validateEmail(to)) {
        Utils.showNotification('请输入有效的邮箱地址', 'error');
        return;
    }
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.post('/emails/send', {
            from: from,
            to: to,
            subject: subject,
            content: content
        });
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.code === 200) {
            Utils.showNotification('邮件发送成功', 'success');
        } else {
            Utils.showNotification(`邮件发送失败: ${response.message}`, 'error');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('邮件发送失败: ' + error.message, 'error');
    }
}

// 测试清除缓存
async function testClearCache() {
    const pattern = document.getElementById('cachePattern').value;
    const resultElement = document.getElementById('result6');
    
    try {
        resultElement.textContent = '请求中...';
        
        const response = await API.clearCache(pattern);
        
        resultElement.textContent = JSON.stringify(response, null, 2);
        
        if (response.code === 200) {
            Utils.showNotification('缓存清除成功', 'success');
        } else {
            Utils.showNotification(`缓存清除失败: ${response.message}`, 'error');
        }
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('缓存清除失败: ' + error.message, 'error');
    }
}

// 复制API响应结果
function copyAPIResult(resultId) {
    const resultElement = document.getElementById(resultId);
    if (resultElement && resultElement.textContent) {
        Utils.copyToClipboard(resultElement.textContent);
    }
}

// 格式化JSON响应
function formatJSONResponse(jsonString) {
    try {
        const obj = JSON.parse(jsonString);
        return JSON.stringify(obj, null, 2);
    } catch (error) {
        return jsonString;
    }
}

// 添加代码高亮（简单实现）
function highlightJSON(element) {
    if (!element) return;
    
    let content = element.textContent;
    
    // 简单的JSON语法高亮
    content = content
        .replace(/"([^"]+)":/g, '<span style="color: #059669;">"$1"</span>:')
        .replace(/: "([^"]+)"/g, ': <span style="color: #dc2626;">"$1"</span>')
        .replace(/: (\d+)/g, ': <span style="color: #2563eb;">$1</span>')
        .replace(/: (true|false)/g, ': <span style="color: #7c3aed;">$1</span>')
        .replace(/: null/g, ': <span style="color: #6b7280;">null</span>');
    
    element.innerHTML = content;
}

// 测试系统指标
async function testSystemMetrics() {
    const resultElement = document.getElementById('result7');

    try {
        resultElement.textContent = '请求中...';

        const response = await API.request('/health/metrics');

        resultElement.textContent = JSON.stringify(response, null, 2);

        Utils.showNotification('获取系统指标成功', 'success');
    } catch (error) {
        resultElement.textContent = `错误: ${error.message}`;
        Utils.showNotification('获取系统指标失败: ' + error.message, 'error');
    }
}

// 导出测试函数供HTML调用
window.testGetEmails = testGetEmails;
window.testGetEmailDetail = testGetEmailDetail;
window.testHealthCheck = testHealthCheck;
window.testCacheStats = testCacheStats;
window.testSendEmail = testSendEmail;
window.testClearCache = testClearCache;
window.testSystemMetrics = testSystemMetrics;
window.copyAPIResult = copyAPIResult;
