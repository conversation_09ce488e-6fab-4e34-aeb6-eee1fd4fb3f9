package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"github.com/beego/beego/v2/server/web"
	_ "goemail/routers"
	"goemail/models"
	"goemail/services/cache"
)

func main() {
	var (
		addresses = flag.String("addresses", "", "要预热的邮箱地址列表，用逗号分隔")
		all       = flag.Bool("all", false, "预热所有邮箱地址")
		help      = flag.Bool("help", false, "显示帮助信息")
	)
	flag.Parse()

	if *help {
		printHelp()
		return
	}

	// 初始化配置
	err := web.LoadAppConfig("ini", "../conf/app.conf")
	if err != nil {
		log.Fatal("加载配置文件失败:", err)
	}

	// 初始化Redis服务
	cache.InitRedisService()

	if *all {
		warmupAllAddresses()
	} else if *addresses != "" {
		addressList := strings.Split(*addresses, ",")
		for _, addr := range addressList {
			addr = strings.TrimSpace(addr)
			if addr != "" {
				warmupAddress(addr)
			}
		}
	} else {
		fmt.Println("请指定要预热的邮箱地址或使用 -all 参数")
		printHelp()
		os.Exit(1)
	}
}

func printHelp() {
	fmt.Println("缓存预热工具")
	fmt.Println("")
	fmt.Println("用法:")
	fmt.Println("  go run cache_warmup.go -addresses=<EMAIL>,<EMAIL>")
	fmt.Println("  go run cache_warmup.go -all")
	fmt.Println("")
	fmt.Println("参数:")
	fmt.Println("  -addresses  要预热的邮箱地址列表，用逗号分隔")
	fmt.Println("  -all        预热所有邮箱地址")
	fmt.Println("  -help       显示此帮助信息")
}

func warmupAllAddresses() {
	log.Println("开始预热所有邮箱地址的缓存...")
	
	// 获取所有唯一的邮箱地址
	addresses, err := getAllEmailAddresses()
	if err != nil {
		log.Fatal("获取邮箱地址列表失败:", err)
	}

	log.Printf("找到 %d 个邮箱地址", len(addresses))

	for i, address := range addresses {
		log.Printf("预热进度: %d/%d - %s", i+1, len(addresses), address)
		warmupAddress(address)
		
		// 避免过于频繁的操作
		time.Sleep(100 * time.Millisecond)
	}

	log.Println("所有邮箱地址缓存预热完成")
}

func getAllEmailAddresses() ([]string, error) {
	emailManager := models.NewEmailManager()
	
	// 这里需要在EmailManager中添加获取所有邮箱地址的方法
	// 暂时返回空列表
	return []string{}, nil
}

func warmupAddress(address string) {
	log.Printf("开始预热邮箱: %s", address)
	
	emailManager := models.NewEmailManager()
	
	start := time.Now()
	err := emailManager.WarmupCache(address)
	duration := time.Since(start)
	
	if err != nil {
		log.Printf("预热失败 %s: %v (耗时: %v)", address, err, duration)
	} else {
		log.Printf("预热成功 %s (耗时: %v)", address, duration)
	}
}
