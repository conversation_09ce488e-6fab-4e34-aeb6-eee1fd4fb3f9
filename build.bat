@echo off
echo === Temporary Email System - Cross Platform Build ===

REM Check Go environment
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go is not installed
    pause
    exit /b 1
)

echo Starting cross-platform compilation...

REM Create build directories
if not exist "build" mkdir build
if not exist "build\windows" mkdir build\windows
if not exist "build\linux" mkdir build\linux

echo.
echo Installing dependencies...
go mod tidy
if %errorlevel% neq 0 (
    echo Error: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Building Windows version...
set GOOS=windows
set GOARCH=amd64
go build -ldflags="-s -w" -o build\windows\temp-email-system.exe .
if %errorlevel% neq 0 (
    echo Error: Windows build failed
    pause
    exit /b 1
)
echo Success: Windows build completed: build\windows\temp-email-system.exe

echo.
echo Building Linux version...
set GOOS=linux
set GOARCH=amd64
go build -ldflags="-s -w" -o build\linux\temp-email-system .
if %errorlevel% neq 0 (
    echo Error: Linux build failed
    pause
    exit /b 1
)
echo Success: Linux build completed: build\linux\temp-email-system

echo.
echo Copying configuration files and documentation...

REM Copy files for Windows version
xcopy /E /I /Y conf build\windows\conf >nul
copy README.md build\windows\ >nul
copy REDIS_CACHE_GUIDE.md build\windows\ >nul
copy docker-compose.yml build\windows\ >nul
copy init.sql build\windows\ >nul
copy start.bat build\windows\ >nul

REM Copy files for Linux version
xcopy /E /I /Y conf build\linux\conf >nul
copy README.md build\linux\ >nul
copy REDIS_CACHE_GUIDE.md build\linux\ >nul
copy docker-compose.yml build\linux\ >nul
copy init.sql build\linux\ >nul
copy start.sh build\linux\ >nul

echo.
echo Creating release packages...

REM Create Windows release package
if exist "temp-email-system-windows.zip" del "temp-email-system-windows.zip"
powershell -Command "Compress-Archive -Path 'build\windows\*' -DestinationPath 'temp-email-system-windows.zip'"
if %errorlevel% equ 0 (
    echo Success: Windows package created: temp-email-system-windows.zip
) else (
    echo Warning: Windows package creation failed, please manually package build\windows directory
)

REM Create Linux release package
if exist "temp-email-system-linux.tar.gz" del "temp-email-system-linux.tar.gz"
powershell -Command "& { cd build\linux; tar -czf '..\..\temp-email-system-linux.tar.gz' * }"
if %errorlevel% equ 0 (
    echo Success: Linux package created: temp-email-system-linux.tar.gz
) else (
    echo Warning: Linux package creation failed, please manually package build\linux directory
)

echo.
echo Build Results:
echo.
dir /B build\windows\temp-email-system.exe 2>nul && (
    for %%F in (build\windows\temp-email-system.exe) do echo    Windows: %%~zF bytes
)
dir /B build\linux\temp-email-system 2>nul && (
    for %%F in (build\linux\temp-email-system) do echo    Linux:   %%~zF bytes
)

echo.
echo Cross-platform compilation completed!
echo.
echo Output directories:
echo    build\windows\     - Windows version files
echo    build\linux\       - Linux version files
echo.
echo Release packages:
if exist "temp-email-system-windows.zip" echo    temp-email-system-windows.zip
if exist "temp-email-system-linux.tar.gz" echo    temp-email-system-linux.tar.gz
echo.

pause
