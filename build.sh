#!/bin/bash

echo "=== 临时邮箱系统 - 跨平台打包脚本 ==="

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go语言环境"
    exit 1
fi

echo "📦 开始跨平台编译..."

# 创建构建目录
mkdir -p build/windows
mkdir -p build/linux

echo ""
echo "🔧 安装依赖包..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败"
    exit 1
fi

echo ""
echo "🏗️  编译Windows版本..."
GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o build/windows/temp-email-system.exe .
if [ $? -ne 0 ]; then
    echo "❌ Windows版本编译失败"
    exit 1
fi
echo "✅ Windows版本编译成功: build/windows/temp-email-system.exe"

echo ""
echo "🐧 编译Linux版本..."
GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o build/linux/temp-email-system .
if [ $? -ne 0 ]; then
    echo "❌ Linux版本编译失败"
    exit 1
fi
echo "✅ Linux版本编译成功: build/linux/temp-email-system"

echo ""
echo "📁 复制配置文件和文档..."

# 复制Windows版本的文件
cp -r conf build/windows/
cp README.md build/windows/
cp REDIS_CACHE_GUIDE.md build/windows/
cp docker-compose.yml build/windows/
cp init.sql build/windows/
cp start.bat build/windows/

# 复制Linux版本的文件
cp -r conf build/linux/
cp README.md build/linux/
cp REDIS_CACHE_GUIDE.md build/linux/
cp docker-compose.yml build/linux/
cp init.sql build/linux/
cp start.sh build/linux/

# 设置Linux启动脚本执行权限
chmod +x build/linux/start.sh
chmod +x build/linux/temp-email-system

echo ""
echo "📦 创建发布包..."

# 创建Windows发布包
if [ -f "temp-email-system-windows.zip" ]; then
    rm "temp-email-system-windows.zip"
fi

if command -v zip &> /dev/null; then
    cd build/windows
    zip -r "../../temp-email-system-windows.zip" .
    cd ../..
    echo "✅ Windows发布包创建成功: temp-email-system-windows.zip"
else
    echo "⚠️  zip命令未找到，请手动打包 build/windows 目录"
fi

# 创建Linux发布包
if [ -f "temp-email-system-linux.tar.gz" ]; then
    rm "temp-email-system-linux.tar.gz"
fi

cd build/linux
tar -czf "../../temp-email-system-linux.tar.gz" .
cd ../..
echo "✅ Linux发布包创建成功: temp-email-system-linux.tar.gz"

echo ""
echo "📊 编译结果:"
echo ""
if [ -f "build/windows/temp-email-system.exe" ]; then
    windows_size=$(stat -c%s "build/windows/temp-email-system.exe" 2>/dev/null || stat -f%z "build/windows/temp-email-system.exe" 2>/dev/null)
    echo "    Windows: $windows_size 字节"
fi

if [ -f "build/linux/temp-email-system" ]; then
    linux_size=$(stat -c%s "build/linux/temp-email-system" 2>/dev/null || stat -f%z "build/linux/temp-email-system" 2>/dev/null)
    echo "    Linux:   $linux_size 字节"
fi

echo ""
echo "🎉 跨平台编译完成！"
echo ""
echo "📁 输出目录:"
echo "    build/windows/     - Windows版本文件"
echo "    build/linux/       - Linux版本文件"
echo ""
echo "📦 发布包:"
[ -f "temp-email-system-windows.zip" ] && echo "    temp-email-system-windows.zip"
[ -f "temp-email-system-linux.tar.gz" ] && echo "    temp-email-system-linux.tar.gz"
echo ""
