package email

import (
	"fmt"
	"net/mail"
	"strings"
	"time"

	"github.com/beego/beego/v2/server/web"
	"github.com/google/uuid"
	"github.com/jhillyerd/enmime"
	"github.com/jordan-wright/email"
	"goemail/models"
)

// EmailService 邮件服务
type EmailService struct {
	emailManager *models.EmailManager
}

// NewEmailService 创建邮件服务实例
func NewEmailService() *EmailService {
	return &EmailService{
		emailManager: models.NewEmailManager(),
	}
}

// ProcessIncomingEmail 处理接收到的邮件
func (es *EmailService) ProcessIncomingEmail(rawEmail []byte, toAddress string) error {
	// 解析邮件
	envelope, err := enmime.ReadEnvelope(strings.NewReader(string(rawEmail)))
	if err != nil {
		return fmt.Errorf("failed to parse email: %v", err)
	}
	
	// 提取邮件信息
	messageId := envelope.GetHeader("Message-ID")
	if messageId == "" {
		messageId = uuid.New().String() + "@temp-email"
	}
	
	fromAddress := envelope.GetHeader("From")
	subject := envelope.GetHeader("Subject")
	bodyText := envelope.Text
	bodyHtml := envelope.HTML
	
	// 创建邮件模型
	emailModel := &models.Email{
		MessageId:   messageId,
		ToAddress:   toAddress,
		FromAddress: fromAddress,
		Subject:     subject,
		BodyText:    bodyText,
		BodyHtml:    bodyHtml,
		RawContent:  string(rawEmail),
		Size:        len(rawEmail),
	}
	
	// 保存邮件
	err = es.emailManager.SaveEmail(emailModel)
	if err != nil {
		return fmt.Errorf("failed to save email: %v", err)
	}
	
	// 处理附件
	for _, attachment := range envelope.Attachments {
		attachmentModel := &models.Attachment{
			Email:       emailModel,
			Filename:    attachment.FileName,
			ContentType: attachment.ContentType,
		}

		// 使用新的SetContentBytes方法设置附件内容
		attachmentModel.SetContentBytes(attachment.Content)

		err = es.emailManager.SaveAttachment(attachmentModel)
		if err != nil {
			// 记录错误但不中断处理
			fmt.Printf("Failed to save attachment %s: %v\n", attachment.FileName, err)
		}
	}
	
	return nil
}

// SendEmail 发送邮件
func (es *EmailService) SendEmail(to, subject, body string, isHtml bool) error {
	e := email.NewEmail()
	e.From = "<EMAIL>"
	e.To = []string{to}
	e.Subject = subject
	
	if isHtml {
		e.HTML = []byte(body)
	} else {
		e.Text = []byte(body)
	}
	
	// 这里需要配置SMTP服务器来发送邮件
	// 暂时返回成功，实际项目中需要配置外部SMTP服务器
	fmt.Printf("Sending email to %s: %s\n", to, subject)
	return nil
}

// ValidateEmailAddress 验证邮箱地址
func (es *EmailService) ValidateEmailAddress(address string) bool {
	_, err := mail.ParseAddress(address)
	return err == nil
}

// IsValidDomain 检查是否是系统支持的域名
func (es *EmailService) IsValidDomain(address string) bool {
	// 从配置中获取支持的域名
	supportedDomain, err := web.AppConfig.String("mail_domain")
	if err != nil || supportedDomain == "" {
		supportedDomain = "yourdomain.com" // 默认域名
	}

	parts := strings.Split(address, "@")
	if len(parts) != 2 {
		return false
	}

	return strings.ToLower(parts[1]) == strings.ToLower(supportedDomain)
}

// GetEmailStats 获取邮件统计信息
func (es *EmailService) GetEmailStats(address string) (map[string]interface{}, error) {
	// 这里可以实现邮件统计功能
	// 比如总邮件数、今日邮件数、未读邮件数等
	stats := map[string]interface{}{
		"total_emails": 0,
		"today_emails": 0,
		"last_email_time": nil,
	}
	
	return stats, nil
}

// CleanupOldEmails 清理旧邮件
func (es *EmailService) CleanupOldEmails(olderThanDays int) error {
	// 实现清理超过指定天数的邮件
	cutoffTime := time.Now().AddDate(0, 0, -olderThanDays)
	fmt.Printf("Cleaning up emails older than %v\n", cutoffTime)
	
	// 这里需要实现具体的清理逻辑
	return nil
}
