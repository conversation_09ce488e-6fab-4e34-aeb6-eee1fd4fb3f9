// 配置页面JavaScript功能

document.addEventListener('DOMContentLoaded', function() {
    initConfigPage();
    bindConfigEvents();
});

// 初始化配置页面
function initConfigPage() {
    console.log('配置页面初始化');
    
    // 默认显示邮件客户端配置
    showConfigSection('email-client');
    
    // 等待系统信息加载完成后更新配置信息
    setTimeout(updateConfigInfo, 1000);
}

// 绑定配置页面事件
function bindConfigEvents() {
    // 侧边栏导航点击事件
    const navItems = document.querySelectorAll('.config-nav .nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            showConfigSection(targetId);
            
            // 更新导航状态
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // 添加配置复制功能
    addCopyButtons();
}

// 显示指定的配置区域
function showConfigSection(sectionId) {
    const sections = document.querySelectorAll('.config-section');
    sections.forEach(section => {
        section.classList.remove('active');
    });
    
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
    }
}

// 更新配置信息
function updateConfigInfo() {
    // 更新所有域名占位符
    const domainElements = document.querySelectorAll('.domain-placeholder');
    domainElements.forEach(el => {
        el.textContent = systemInfo.mail_domain;
    });
    
    // 更新服务器占位符
    const serverElements = document.querySelectorAll('.server-placeholder');
    serverElements.forEach(el => {
        el.textContent = systemInfo.mail_domain;
    });
    
    // 更新端口信息
    updatePortInfo();
    
    console.log('配置信息已更新:', systemInfo);
}

// 更新端口信息
function updatePortInfo() {
    const portMappings = {
        'port-smtp': systemInfo.smtp_port,
        'port-imap': systemInfo.imap_port,
        'port-pop3': systemInfo.pop3_port,
        'port-http': systemInfo.http_port
    };
    
    Object.keys(portMappings).forEach(className => {
        const elements = document.querySelectorAll(`.${className}`);
        elements.forEach(el => {
            el.textContent = portMappings[className];
        });
    });
}

// 添加复制按钮到配置表格
function addCopyButtons() {
    const configTables = document.querySelectorAll('.config-table table');
    
    configTables.forEach(table => {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const valueCell = cells[1];
                const codeElements = valueCell.querySelectorAll('code');
                
                codeElements.forEach(codeEl => {
                    // 为代码元素添加复制功能
                    codeEl.style.cursor = 'pointer';
                    codeEl.title = '点击复制';
                    
                    codeEl.addEventListener('click', function() {
                        Utils.copyToClipboard(this.textContent);
                    });
                    
                    // 添加悬停效果
                    codeEl.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#e0f2fe';
                        this.style.transform = 'scale(1.05)';
                    });
                    
                    codeEl.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '#f1f5f9';
                        this.style.transform = 'scale(1)';
                    });
                });
            }
        });
    });
}

// 生成配置文件内容
function generateConfigFile(type) {
    let config = '';
    
    switch (type) {
        case 'outlook':
            config = generateOutlookConfig();
            break;
        case 'thunderbird':
            config = generateThunderbirdConfig();
            break;
        case 'apple-mail':
            config = generateAppleMailConfig();
            break;
        case 'android':
            config = generateAndroidConfig();
            break;
        default:
            config = generateGenericConfig();
    }
    
    return config;
}

// 生成Outlook配置
function generateOutlookConfig() {
    return `
Outlook配置步骤：

1. 打开Outlook，选择"文件" > "添加账户"
2. 选择"手动设置或其他服务器类型"
3. 选择"POP或IMAP"
4. 填写以下信息：

用户信息：
- 您的姓名: 任意名称
- 电子邮件地址: your-email@${systemInfo.mail_domain}

服务器信息：
接收邮件服务器 (IMAP):
- 服务器类型: IMAP
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.imap_port}
- 加密: 无

发送邮件服务器 (SMTP):
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.smtp_port}
- 加密: 无

登录信息：
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456 (或任意密码)

5. 点击"其他设置"，在"高级"选项卡中：
   - 取消勾选"服务器要求加密连接(SSL)"
   - 确认端口号正确

6. 点击"测试账户设置"验证配置
7. 完成设置
`;
}

// 生成Thunderbird配置
function generateThunderbirdConfig() {
    return `
Thunderbird配置步骤：

1. 打开Thunderbird，选择"账户设置"
2. 点击"账户操作" > "添加邮件账户"
3. 填写以下信息：

基本信息：
- 您的姓名: 任意名称
- 电子邮件地址: your-email@${systemInfo.mail_domain}
- 密码: 123456 (或任意密码)

4. 点击"手动配置"
5. 填写服务器设置：

接收服务器 (IMAP):
- 协议: IMAP
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.imap_port}
- SSL: 无
- 认证: 普通密码

发送服务器 (SMTP):
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.smtp_port}
- SSL: 无
- 认证: 无

6. 点击"重新测试"验证配置
7. 点击"完成"保存设置
`;
}

// 生成Apple Mail配置
function generateAppleMailConfig() {
    return `
Apple Mail配置步骤：

1. 打开"邮件"应用
2. 选择"邮件" > "偏好设置" > "账户"
3. 点击"+"添加账户
4. 选择"其他邮件账户"
5. 填写以下信息：

基本信息：
- 姓名: 任意名称
- 电子邮件地址: your-email@${systemInfo.mail_domain}
- 密码: 123456 (或任意密码)

6. 点击"登录"，然后选择"手动配置"
7. 填写服务器设置：

接收邮件服务器 (IMAP):
- 账户类型: IMAP
- 邮件服务器: ${systemInfo.mail_domain}
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456
- 端口: ${systemInfo.imap_port}
- 使用SSL: 关闭

发送邮件服务器 (SMTP):
- SMTP服务器: ${systemInfo.mail_domain}
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456
- 端口: ${systemInfo.smtp_port}
- 使用SSL: 关闭

8. 点击"登录"完成设置
`;
}

// 生成Android配置
function generateAndroidConfig() {
    return `
Android邮件客户端配置步骤：

1. 打开邮件应用（Gmail、Outlook等）
2. 选择"添加账户" > "其他"
3. 填写以下信息：

基本信息：
- 电子邮件地址: your-email@${systemInfo.mail_domain}
- 密码: 123456 (或任意密码)

4. 选择"手动设置"
5. 选择账户类型: IMAP
6. 填写服务器设置：

接收服务器设置:
- IMAP服务器: ${systemInfo.mail_domain}
- 安全类型: 无
- 端口: ${systemInfo.imap_port}
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456

发送服务器设置:
- SMTP服务器: ${systemInfo.mail_domain}
- 安全类型: 无
- 端口: ${systemInfo.smtp_port}
- 需要登录: 是
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456

7. 完成设置并同步邮件
`;
}

// 生成通用配置
function generateGenericConfig() {
    return `
通用邮件客户端配置：

IMAP设置 (接收邮件):
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.imap_port}
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456 (任意密码)
- 加密: 无 (关闭SSL/TLS)

POP3设置 (接收邮件):
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.pop3_port}
- 用户名: your-email@${systemInfo.mail_domain}
- 密码: 123456 (任意密码)
- 加密: 无 (关闭SSL/TLS)

SMTP设置 (发送邮件):
- 服务器: ${systemInfo.mail_domain}
- 端口: ${systemInfo.smtp_port}
- 用户名: your-email@${systemInfo.mail_domain} (可选)
- 密码: 123456 (可选)
- 认证: 不需要
- 加密: 无

注意事项：
- 邮箱地址必须以 @${systemInfo.mail_domain} 结尾
- 密码可以是任意字符串，系统不验证
- 建议关闭所有SSL/TLS加密选项
- 首次使用会自动创建邮箱
`;
}

// 下载配置文件
function downloadConfig(type) {
    const config = generateConfigFile(type);
    const blob = new Blob([config], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `email-config-${type}.txt`;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    
    Utils.showNotification(`${type}配置文件已下载`, 'success');
}

// 导出函数供HTML调用
window.downloadConfig = downloadConfig;
window.showConfigSection = showConfigSection;
