# 临时邮箱系统 - 跨平台编译Makefile

# 项目信息
APP_NAME = temp-email-system
VERSION = 1.0.0
BUILD_TIME = $(shell date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT = $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 编译参数
LDFLAGS = -s -w -X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)
BUILD_DIR = build
DIST_DIR = dist

# 默认目标
.PHONY: all
all: clean deps build-all package

# 清理构建文件
.PHONY: clean
clean:
	@echo "🧹 清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@rm -rf $(DIST_DIR)
	@rm -f $(APP_NAME)-*.zip
	@rm -f $(APP_NAME)-*.tar.gz
	@rm -f $(APP_NAME).exe
	@rm -f $(APP_NAME)
	@rm -f *.log
	@rm -f *.tmp
	@rm -f *.test
	@rm -f coverage.txt
	@rm -f coverage.html

# 安装依赖
.PHONY: deps
deps:
	@echo "📦 安装依赖包..."
	@go mod tidy
	@go mod download

# 编译所有平台
.PHONY: build-all
build-all: build-windows build-linux build-darwin build-arm

# Windows版本
.PHONY: build-windows
build-windows:
	@echo "🏗️  编译Windows版本..."
	@mkdir -p $(BUILD_DIR)/windows-amd64
	@GOOS=windows GOARCH=amd64 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/windows-amd64/$(APP_NAME).exe .
	@echo "✅ Windows AMD64版本编译完成"

# Linux版本
.PHONY: build-linux
build-linux:
	@echo "🐧 编译Linux版本..."
	@mkdir -p $(BUILD_DIR)/linux-amd64
	@GOOS=linux GOARCH=amd64 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/linux-amd64/$(APP_NAME) .
	@echo "✅ Linux AMD64版本编译完成"

# macOS版本
.PHONY: build-darwin
build-darwin:
	@echo "🍎 编译macOS版本..."
	@mkdir -p $(BUILD_DIR)/darwin-amd64
	@GOOS=darwin GOARCH=amd64 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/darwin-amd64/$(APP_NAME) .
	@mkdir -p $(BUILD_DIR)/darwin-arm64
	@GOOS=darwin GOARCH=arm64 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/darwin-arm64/$(APP_NAME) .
	@echo "✅ macOS版本编译完成"

# ARM版本（适用于树莓派等）
.PHONY: build-arm
build-arm:
	@echo "🔧 编译ARM版本..."
	@mkdir -p $(BUILD_DIR)/linux-arm64
	@GOOS=linux GOARCH=arm64 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/linux-arm64/$(APP_NAME) .
	@mkdir -p $(BUILD_DIR)/linux-arm
	@GOOS=linux GOARCH=arm GOARM=7 go build -ldflags="$(LDFLAGS)" -o $(BUILD_DIR)/linux-arm/$(APP_NAME) .
	@echo "✅ ARM版本编译完成"

# 复制配置文件
.PHONY: copy-files
copy-files:
	@echo "📁 复制配置文件和文档..."
	@for dir in $(BUILD_DIR)/*/; do \
		cp -r conf "$$dir"; \
		cp README.md "$$dir"; \
		cp REDIS_CACHE_GUIDE.md "$$dir"; \
		cp docker-compose.yml "$$dir"; \
		cp init.sql "$$dir"; \
		if [[ "$$dir" == *"windows"* ]]; then \
			cp start.bat "$$dir"; \
		else \
			cp start.sh "$$dir"; \
			chmod +x "$$dir/start.sh"; \
			chmod +x "$$dir/$(APP_NAME)"; \
		fi; \
	done

# 打包发布
.PHONY: package
package: copy-files
	@echo "📦 创建发布包..."
	@mkdir -p $(DIST_DIR)
	
	# Windows包
	@if [ -d "$(BUILD_DIR)/windows-amd64" ]; then \
		cd $(BUILD_DIR)/windows-amd64 && zip -r "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-windows-amd64.zip" .; \
		echo "✅ Windows AMD64发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-windows-amd64.zip"; \
	fi
	
	# Linux包
	@if [ -d "$(BUILD_DIR)/linux-amd64" ]; then \
		cd $(BUILD_DIR)/linux-amd64 && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz" .; \
		echo "✅ Linux AMD64发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz"; \
	fi
	
	# macOS包
	@if [ -d "$(BUILD_DIR)/darwin-amd64" ]; then \
		cd $(BUILD_DIR)/darwin-amd64 && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-darwin-amd64.tar.gz" .; \
		echo "✅ macOS AMD64发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-darwin-amd64.tar.gz"; \
	fi
	
	@if [ -d "$(BUILD_DIR)/darwin-arm64" ]; then \
		cd $(BUILD_DIR)/darwin-arm64 && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-darwin-arm64.tar.gz" .; \
		echo "✅ macOS ARM64发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-darwin-arm64.tar.gz"; \
	fi
	
	# ARM包
	@if [ -d "$(BUILD_DIR)/linux-arm64" ]; then \
		cd $(BUILD_DIR)/linux-arm64 && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-arm64.tar.gz" .; \
		echo "✅ Linux ARM64发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-arm64.tar.gz"; \
	fi
	
	@if [ -d "$(BUILD_DIR)/linux-arm" ]; then \
		cd $(BUILD_DIR)/linux-arm && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-arm.tar.gz" .; \
		echo "✅ Linux ARM发布包: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-arm.tar.gz"; \
	fi

# 快速编译（仅Windows和Linux）
.PHONY: quick
quick: clean deps
	@echo "⚡ 快速编译Windows和Linux版本..."
	@make build-windows build-linux copy-files
	@mkdir -p $(DIST_DIR)
	@cd $(BUILD_DIR)/windows-amd64 && zip -r "../../$(DIST_DIR)/$(APP_NAME)-windows.zip" .
	@cd $(BUILD_DIR)/linux-amd64 && tar -czf "../../$(DIST_DIR)/$(APP_NAME)-linux.tar.gz" .
	@echo "🎉 快速编译完成！"

# 开发模式编译
.PHONY: dev
dev:
	@echo "🔧 开发模式编译..."
	@go build -race -o $(APP_NAME) .
	@echo "✅ 开发版本编译完成: ./$(APP_NAME)"

# 运行测试
.PHONY: test
test:
	@echo "🧪 运行测试..."
	@go test -v ./...

# 代码检查
.PHONY: lint
lint:
	@echo "🔍 代码检查..."
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint未安装，跳过代码检查"; \
	fi

# 显示版本信息
.PHONY: version
version:
	@echo "项目: $(APP_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Git提交: $(GIT_COMMIT)"

# 显示帮助信息
.PHONY: help
help:
	@echo "临时邮箱系统 - 编译脚本"
	@echo ""
	@echo "可用命令:"
	@echo "  all          - 完整编译所有平台并打包"
	@echo "  quick        - 快速编译Windows和Linux版本"
	@echo "  dev          - 开发模式编译"
	@echo "  clean        - 清理构建文件"
	@echo "  deps         - 安装依赖"
	@echo "  test         - 运行测试"
	@echo "  lint         - 代码检查"
	@echo "  version      - 显示版本信息"
	@echo ""
	@echo "平台编译:"
	@echo "  build-windows - 编译Windows版本"
	@echo "  build-linux   - 编译Linux版本"
	@echo "  build-darwin  - 编译macOS版本"
	@echo "  build-arm     - 编译ARM版本"
	@echo ""
	@echo "示例:"
	@echo "  make quick           # 快速编译"
	@echo "  make build-linux     # 仅编译Linux版本"
	@echo "  make all             # 完整编译所有平台"

# 显示编译结果
.PHONY: info
info:
	@echo "📊 编译结果:"
	@echo ""
	@for file in $(BUILD_DIR)/*/*; do \
		if [ -f "$$file" ] && [ -x "$$file" ]; then \
			size=$$(stat -c%s "$$file" 2>/dev/null || stat -f%z "$$file" 2>/dev/null); \
			echo "  $$(basename $$(dirname $$file)): $$size 字节"; \
		fi; \
	done
	@echo ""
	@if [ -d "$(DIST_DIR)" ]; then \
		echo "📦 发布包:"; \
		ls -la $(DIST_DIR)/; \
	fi
