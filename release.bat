@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 临时邮箱系统发布脚本 - Windows版本

REM 项目信息
set APP_NAME=temp-email-system
if "%VERSION%"=="" set VERSION=1.0.0
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set BUILD_DATE=%%c-%%a-%%b
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set BUILD_TIME=%%a:%%b
set BUILD_TIME=%BUILD_DATE%_%BUILD_TIME%

REM Git提交信息
for /f %%i in ('git rev-parse --short HEAD 2^>nul') do set GIT_COMMIT=%%i
if "%GIT_COMMIT%"=="" set GIT_COMMIT=unknown

REM 目录定义
set BUILD_DIR=build
set DIST_DIR=dist
set RELEASE_DIR=release

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    临时邮箱系统发布脚本                      ║
echo ║                Temporary Email System Release                ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║ 版本: %VERSION%
echo ║ 构建时间: %BUILD_TIME%
echo ║ Git提交: %GIT_COMMIT%
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 解析命令行参数
set COMMAND=%1
if "%COMMAND%"=="" set COMMAND=all

if "%COMMAND%"=="clean" goto :clean
if "%COMMAND%"=="deps" goto :deps
if "%COMMAND%"=="test" goto :test
if "%COMMAND%"=="build" goto :build
if "%COMMAND%"=="package" goto :package
if "%COMMAND%"=="all" goto :all
if "%COMMAND%"=="help" goto :help

echo ❌ 未知命令: %COMMAND%
echo 使用 '%0 help' 查看帮助
exit /b 1

:clean
echo 🧹 清理构建文件...
if exist %BUILD_DIR% rmdir /s /q %BUILD_DIR%
if exist %DIST_DIR% rmdir /s /q %DIST_DIR%
if exist %RELEASE_DIR% rmdir /s /q %RELEASE_DIR%
del /q %APP_NAME%-*.zip 2>nul
del /q %APP_NAME%-*.tar.gz 2>nul
echo ✅ 清理完成
goto :end

:deps
echo 📦 安装Go依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    exit /b 1
)
go mod download
echo ✅ 依赖安装完成
goto :end

:test
echo 🧪 运行测试...
go test -v ./...
if %errorlevel% neq 0 (
    echo ❌ 测试失败
    exit /b 1
)
echo ✅ 所有测试通过
goto :end

:build
echo 🔧 检查Go环境...
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go未安装，请先安装Go语言环境
    exit /b 1
)

call :deps

echo 🏗️  开始编译所有平台...

REM 创建构建目录
if not exist %BUILD_DIR% mkdir %BUILD_DIR%
if not exist %BUILD_DIR%\windows-amd64 mkdir %BUILD_DIR%\windows-amd64
if not exist %BUILD_DIR%\linux-amd64 mkdir %BUILD_DIR%\linux-amd64
if not exist %BUILD_DIR%\linux-arm64 mkdir %BUILD_DIR%\linux-arm64
if not exist %BUILD_DIR%\linux-arm mkdir %BUILD_DIR%\linux-arm
if not exist %BUILD_DIR%\darwin-amd64 mkdir %BUILD_DIR%\darwin-amd64
if not exist %BUILD_DIR%\darwin-arm64 mkdir %BUILD_DIR%\darwin-arm64

set LDFLAGS=-s -w -X main.Version=%VERSION% -X main.BuildTime=%BUILD_TIME% -X main.GitCommit=%GIT_COMMIT%

echo   编译 Windows AMD64...
set GOOS=windows
set GOARCH=amd64
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\windows-amd64\%APP_NAME%.exe .
if %errorlevel% neq 0 (
    echo ❌ Windows AMD64编译失败
    exit /b 1
)
echo ✅ Windows AMD64编译完成

echo   编译 Linux AMD64...
set GOOS=linux
set GOARCH=amd64
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\linux-amd64\%APP_NAME% .
if %errorlevel% neq 0 (
    echo ❌ Linux AMD64编译失败
    exit /b 1
)
echo ✅ Linux AMD64编译完成

echo   编译 Linux ARM64...
set GOOS=linux
set GOARCH=arm64
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\linux-arm64\%APP_NAME% .
if %errorlevel% neq 0 (
    echo ❌ Linux ARM64编译失败
    exit /b 1
)
echo ✅ Linux ARM64编译完成

echo   编译 Linux ARM...
set GOOS=linux
set GOARCH=arm
set GOARM=7
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\linux-arm\%APP_NAME% .
if %errorlevel% neq 0 (
    echo ❌ Linux ARM编译失败
    exit /b 1
)
echo ✅ Linux ARM编译完成

echo   编译 macOS AMD64...
set GOOS=darwin
set GOARCH=amd64
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\darwin-amd64\%APP_NAME% .
if %errorlevel% neq 0 (
    echo ❌ macOS AMD64编译失败
    exit /b 1
)
echo ✅ macOS AMD64编译完成

echo   编译 macOS ARM64...
set GOOS=darwin
set GOARCH=arm64
go build -ldflags="%LDFLAGS%" -o %BUILD_DIR%\darwin-arm64\%APP_NAME% .
if %errorlevel% neq 0 (
    echo ❌ macOS ARM64编译失败
    exit /b 1
)
echo ✅ macOS ARM64编译完成

echo 📁 复制配置文件和文档...
for /d %%d in (%BUILD_DIR%\*) do (
    xcopy /E /I /Y conf "%%d\conf" >nul
    copy README.md "%%d\" >nul
    copy REDIS_CACHE_GUIDE.md "%%d\" >nul
    copy docker-compose.yml "%%d\" >nul
    copy init.sql "%%d\" >nul
    
    REM 根据平台复制启动脚本
    echo %%d | findstr /i "windows" >nul
    if !errorlevel! equ 0 (
        copy start.bat "%%d\" >nul
    ) else (
        copy start.sh "%%d\" >nul
    )
    
    REM 创建版本文件
    echo 应用名称: %APP_NAME% > "%%d\VERSION"
    echo 版本: %VERSION% >> "%%d\VERSION"
    echo 构建时间: %BUILD_TIME% >> "%%d\VERSION"
    echo Git提交: %GIT_COMMIT% >> "%%d\VERSION"
    for %%f in ("%%d") do echo 平台: %%~nxf >> "%%d\VERSION"
)
echo ✅ 文件复制完成
goto :end

:package
echo 📦 创建发布包...
if not exist %DIST_DIR% mkdir %DIST_DIR%

for /d %%d in (%BUILD_DIR%\*) do (
    for %%f in ("%%d") do set PLATFORM=%%~nxf
    set PACKAGE_NAME=%APP_NAME%-%VERSION%-!PLATFORM!
    
    echo !PLATFORM! | findstr /i "windows" >nul
    if !errorlevel! equ 0 (
        REM Windows使用zip格式
        cd "%%d"
        powershell -Command "Compress-Archive -Path '*' -DestinationPath '../../%DIST_DIR%/!PACKAGE_NAME!.zip' -Force"
        cd ..\..
        echo ✅ 创建Windows包: !PACKAGE_NAME!.zip
    ) else (
        REM 其他平台使用tar.gz格式
        cd "%%d"
        powershell -Command "& { tar -czf '../../%DIST_DIR%/!PACKAGE_NAME!.tar.gz' * }"
        cd ..\..
        echo ✅ 创建包: !PACKAGE_NAME!.tar.gz
    )
)
goto :end

:all
call :clean
call :deps
call :test
call :build
call :package

echo 📊 构建结果统计:
echo.
for /d %%d in (%BUILD_DIR%\*) do (
    for %%f in ("%%d") do set PLATFORM=%%~nxf
    echo !PLATFORM! | findstr /i "windows" >nul
    if !errorlevel! equ 0 (
        if exist "%%d\%APP_NAME%.exe" (
            for %%s in ("%%d\%APP_NAME%.exe") do echo   !PLATFORM!: %%~zs 字节
        )
    ) else (
        if exist "%%d\%APP_NAME%" (
            for %%s in ("%%d\%APP_NAME%") do echo   !PLATFORM!: %%~zs 字节
        )
    )
)

echo.
echo 📦 发布包:
if exist %DIST_DIR% dir /b %DIST_DIR%\*

echo.
echo 🎉 完整发布流程完成！
goto :end

:help
echo 用法: %0 [命令]
echo.
echo 命令:
echo   all      - 完整发布流程（默认）
echo   clean    - 清理构建文件
echo   deps     - 安装依赖
echo   test     - 运行测试
echo   build    - 编译所有平台
echo   package  - 创建发布包
echo   help     - 显示帮助
echo.
echo 示例:
echo   %0 all     - 完整发布流程
echo   %0 build   - 仅编译
echo   %0 clean   - 清理文件
goto :end

:end
echo.
echo ✅ 操作完成！
pause
