<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置说明 - 临时邮箱系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/config.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>📧 临时邮箱系统</h1>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">首页</a>
                <a href="/mailbox" class="nav-link">邮箱管理</a>
                <a href="/api" class="nav-link">API文档</a>
                <a href="/config" class="nav-link active">配置说明</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="config-layout">
                <!-- 侧边栏导航 -->
                <aside class="config-sidebar">
                    <h3>配置指南</h3>
                    <nav class="config-nav">
                        <a href="#email-client" class="nav-item active">邮件客户端配置</a>
                        <a href="#dns-config" class="nav-item">DNS配置</a>
                        <a href="#server-config" class="nav-item">服务器配置</a>
                        <a href="#troubleshooting" class="nav-item">故障排查</a>
                    </nav>
                </aside>

                <!-- 配置内容 -->
                <div class="config-content">
                    <!-- 邮件客户端配置 -->
                    <section class="config-section" id="email-client">
                        <h2>邮件客户端配置</h2>
                        
                        <div class="config-card">
                            <h3>🔑 密码机制说明</h3>
                            <p>本系统采用<strong>无需注册</strong>设计，密码验证规则：</p>
                            <ul>
                                <li><strong>用户名</strong>：必须是有效邮箱格式（如：<code>test@<span class="domain-placeholder">yourdomain.com</span></code>）</li>
                                <li><strong>密码</strong>：可以是任意字符串，系统不验证密码内容</li>
                                <li><strong>推荐密码</strong>：<code>123456</code>、<code>password</code>、<code>temp</code> 或空密码</li>
                            </ul>
                        </div>

                        <div class="config-card">
                            <h3>📥 IMAP配置（推荐用于邮件客户端）</h3>
                            <div class="config-table">
                                <table>
                                    <tr>
                                        <td>服务器</td>
                                        <td><code class="server-placeholder">yourdomain.com</code> (或服务器IP地址)</td>
                                    </tr>
                                    <tr>
                                        <td>端口</td>
                                        <td><code class="port-imap">1143</code></td>
                                    </tr>
                                    <tr>
                                        <td>用户名</td>
                                        <td>任意@<span class="domain-placeholder">yourdomain.com</span>邮箱地址</td>
                                    </tr>
                                    <tr>
                                        <td>密码</td>
                                        <td>任意字符串（推荐：123456）</td>
                                    </tr>
                                    <tr>
                                        <td>加密</td>
                                        <td>无（关闭SSL/STARTTLS）</td>
                                    </tr>
                                    <tr>
                                        <td>认证方式</td>
                                        <td>普通密码</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="config-card">
                            <h3>📥 POP3配置（简单邮件下载）</h3>
                            <div class="config-table">
                                <table>
                                    <tr>
                                        <td>服务器</td>
                                        <td><code class="server-placeholder">yourdomain.com</code> (或服务器IP地址)</td>
                                    </tr>
                                    <tr>
                                        <td>端口</td>
                                        <td><code class="port-pop3">1110</code></td>
                                    </tr>
                                    <tr>
                                        <td>用户名</td>
                                        <td>任意@<span class="domain-placeholder">yourdomain.com</span>邮箱地址</td>
                                    </tr>
                                    <tr>
                                        <td>密码</td>
                                        <td>任意字符串（推荐：123456）</td>
                                    </tr>
                                    <tr>
                                        <td>加密</td>
                                        <td>无（关闭SSL/TLS）</td>
                                    </tr>
                                    <tr>
                                        <td>认证方式</td>
                                        <td>普通密码</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="config-card">
                            <h3>📤 SMTP发送配置</h3>
                            <div class="config-table">
                                <table>
                                    <tr>
                                        <td>服务器</td>
                                        <td><code class="server-placeholder">yourdomain.com</code> (或服务器IP地址)</td>
                                    </tr>
                                    <tr>
                                        <td>端口</td>
                                        <td><code class="port-smtp">2525</code></td>
                                    </tr>
                                    <tr>
                                        <td>用户名</td>
                                        <td>任意@<span class="domain-placeholder">yourdomain.com</span>邮箱地址（可选）</td>
                                    </tr>
                                    <tr>
                                        <td>密码</td>
                                        <td>任意字符串（可选）</td>
                                    </tr>
                                    <tr>
                                        <td>认证</td>
                                        <td>不需要（或使用任意用户名密码）</td>
                                    </tr>
                                    <tr>
                                        <td>加密</td>
                                        <td>无</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="config-card">
                            <h3>💡 使用示例</h3>
                            <div class="example-box">
                                <h4>假设要使用邮箱：test123@<span class="domain-placeholder">yourdomain.com</span></h4>
                                <div class="example-config">
                                    <p><strong>配置参数：</strong></p>
                                    <ul>
                                        <li>服务器：<code>mail.<span class="domain-placeholder">yourdomain.com</span></code></li>
                                        <li>IMAP端口：<code class="port-imap">1143</code></li>
                                        <li>用户名：<code>test123@<span class="domain-placeholder">yourdomain.com</span></code></li>
                                        <li>密码：<code>123456</code>（或任意字符串）</li>
                                    </ul>
                                    <p><strong>配置完成后：</strong></p>
                                    <ul class="success-list">
                                        <li>✅ 立即可以接收发送到 test123@<span class="domain-placeholder">yourdomain.com</span> 的邮件</li>
                                        <li>✅ 无需注册或验证过程</li>
                                        <li>✅ 支持多个不同的邮箱地址同时使用</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="config-card">
                            <h3>⚠️ 注意事项</h3>
                            <ul class="warning-list">
                                <li>邮箱地址的域名部分必须匹配系统配置的域名</li>
                                <li>首次使用某个邮箱地址时，系统会自动创建</li>
                                <li>所有邮件都存储在数据库中，支持历史邮件查看</li>
                                <li>建议在邮件客户端中关闭SSL/TLS加密（开发环境）</li>
                            </ul>
                        </div>
                    </section>

                    <!-- DNS配置 -->
                    <section class="config-section" id="dns-config" style="display: none;">
                        <h2>DNS配置</h2>
                        <div class="config-card">
                            <h3>必需的DNS记录</h3>
                            <p>要使邮件系统正常工作，需要配置以下DNS记录：</p>
                            
                            <h4>1. MX记录（邮件交换记录）</h4>
                            <div class="code-block">
                                <code>yourdomain.com. IN MX 10 mail.yourdomain.com.</code>
                            </div>
                            
                            <h4>2. A记录（主机记录）</h4>
                            <div class="code-block">
                                <code>mail.yourdomain.com. IN A 你的服务器IP地址</code>
                            </div>
                            
                            <h4>3. SPF记录（防止邮件伪造）</h4>
                            <div class="code-block">
                                <code>yourdomain.com. IN TXT "v=spf1 ip4:你的服务器IP ~all"</code>
                            </div>
                        </div>
                    </section>

                    <!-- 服务器配置 -->
                    <section class="config-section" id="server-config" style="display: none;">
                        <h2>服务器配置</h2>
                        <div class="config-card">
                            <h3>端口配置</h3>
                            <p>确保以下端口在防火墙中开放：</p>
                            <ul>
                                <li><strong>HTTP:</strong> <code class="port-http">8080</code> - Web界面和API</li>
                                <li><strong>SMTP:</strong> <code class="port-smtp">2525</code> - 邮件接收</li>
                                <li><strong>IMAP:</strong> <code class="port-imap">1143</code> - 邮件读取</li>
                                <li><strong>POP3:</strong> <code class="port-pop3">1110</code> - 邮件下载</li>
                            </ul>
                        </div>
                    </section>

                    <!-- 故障排查 -->
                    <section class="config-section" id="troubleshooting" style="display: none;">
                        <h2>故障排查</h2>
                        <div class="config-card">
                            <h3>常见问题</h3>
                            <div class="faq-item">
                                <h4>Q: 无法连接到邮件服务器</h4>
                                <p>A: 检查防火墙设置，确保相应端口已开放。检查服务器是否正在运行。</p>
                            </div>
                            <div class="faq-item">
                                <h4>Q: 邮件客户端提示认证失败</h4>
                                <p>A: 确保用户名是完整的邮箱地址格式，密码可以是任意字符串。</p>
                            </div>
                            <div class="faq-item">
                                <h4>Q: 收不到邮件</h4>
                                <p>A: 检查DNS配置是否正确，MX记录是否指向正确的服务器。</p>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </main>

    <script src="/static/js/common.js"></script>
    <script src="/static/js/config.js"></script>
</body>
</html>
