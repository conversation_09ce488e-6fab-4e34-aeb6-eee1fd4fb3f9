package controllers

import (
	"fmt"
	"runtime"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"temp-email-system/services/cache"
)

// HealthController 健康检查控制器
type HealthController struct {
	BaseController
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"`
	Timestamp string                 `json:"timestamp"`
	Version   string                 `json:"version"`
	Uptime    string                 `json:"uptime"`
	System    SystemInfo             `json:"system"`
	Services  map[string]ServiceInfo `json:"services"`
}

// SystemInfo 系统信息
type SystemInfo struct {
	GoVersion    string `json:"go_version"`
	NumGoroutine int    `json:"num_goroutine"`
	NumCPU       int    `json:"num_cpu"`
	MemoryUsage  string `json:"memory_usage"`
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
	Latency string `json:"latency,omitempty"`
}

var startTime = time.Now()

// Check 健康检查
func (c *HealthController) Check() {
	status := c.performHealthCheck()
	
	if status.Status == "healthy" {
		c.SuccessResponse(status)
	} else {
		c.ErrorResponse(503, "服务不健康")
		c.Data["json"] = status
		c.ServeJSON()
	}
}

// performHealthCheck 执行健康检查
func (c *HealthController) performHealthCheck() *HealthStatus {
	status := &HealthStatus{
		Status:    "healthy",
		Timestamp: time.Now().Format(time.RFC3339),
		Version:   "1.0.0", // 应该从配置或构建信息获取
		Uptime:    time.Since(startTime).String(),
		System:    c.getSystemInfo(),
		Services:  make(map[string]ServiceInfo),
	}
	
	// 检查数据库
	dbStatus := c.checkDatabase()
	status.Services["database"] = dbStatus
	if dbStatus.Status != "healthy" {
		status.Status = "unhealthy"
	}
	
	// 检查Redis
	redisStatus := c.checkRedis()
	status.Services["redis"] = redisStatus
	if redisStatus.Status != "healthy" {
		status.Status = "degraded" // Redis不可用时降级服务
	}
	
	return status
}

// getSystemInfo 获取系统信息
func (c *HealthController) getSystemInfo() SystemInfo {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	return SystemInfo{
		GoVersion:    runtime.Version(),
		NumGoroutine: runtime.NumGoroutine(),
		NumCPU:       runtime.NumCPU(),
		MemoryUsage:  fmt.Sprintf("%.2f MB", float64(m.Alloc)/1024/1024),
	}
}

// checkDatabase 检查数据库连接
func (c *HealthController) checkDatabase() ServiceInfo {
	start := time.Now()
	
	o := orm.NewOrm()
	var result int
	err := o.Raw("SELECT 1").QueryRow(&result)
	
	latency := time.Since(start)
	
	if err != nil {
		return ServiceInfo{
			Status:  "unhealthy",
			Message: fmt.Sprintf("数据库连接失败: %v", err),
			Latency: latency.String(),
		}
	}
	
	return ServiceInfo{
		Status:  "healthy",
		Latency: latency.String(),
	}
}

// checkRedis 检查Redis连接
func (c *HealthController) checkRedis() ServiceInfo {
	start := time.Now()
	
	redisService := cache.GetRedisService()
	if !redisService.IsEnabled() {
		return ServiceInfo{
			Status:  "disabled",
			Message: "Redis缓存已禁用",
		}
	}
	
	// 尝试ping Redis
	err := redisService.Ping()
	latency := time.Since(start)
	
	if err != nil {
		return ServiceInfo{
			Status:  "unhealthy",
			Message: fmt.Sprintf("Redis连接失败: %v", err),
			Latency: latency.String(),
		}
	}
	
	return ServiceInfo{
		Status:  "healthy",
		Latency: latency.String(),
	}
}

// Metrics 获取系统指标
func (c *HealthController) Metrics() {
	metrics := c.getMetrics()
	c.SuccessResponse(metrics)
}

// getMetrics 获取详细指标
func (c *HealthController) getMetrics() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	// 获取缓存统计
	redisService := cache.GetRedisService()
	cacheStats := redisService.GetStats()
	
	return map[string]interface{}{
		"timestamp": time.Now().Format(time.RFC3339),
		"uptime":    time.Since(startTime).String(),
		"system": map[string]interface{}{
			"go_version":     runtime.Version(),
			"num_goroutine":  runtime.NumGoroutine(),
			"num_cpu":        runtime.NumCPU(),
			"memory": map[string]interface{}{
				"alloc":         m.Alloc,
				"total_alloc":   m.TotalAlloc,
				"sys":           m.Sys,
				"num_gc":        m.NumGC,
				"gc_cpu_fraction": m.GCCPUFraction,
			},
		},
		"cache": map[string]interface{}{
			"enabled":     redisService.IsEnabled(),
			"hit_count":   cacheStats.HitCount,
			"miss_count":  cacheStats.MissCount,
			"error_count": cacheStats.ErrorCount,
			"hit_rate":    cacheStats.HitRate,
		},
	}
}

// Ready 就绪检查（用于Kubernetes）
func (c *HealthController) Ready() {
	// 检查关键服务是否就绪
	dbStatus := c.checkDatabase()
	
	if dbStatus.Status == "healthy" {
		c.SuccessResponse(map[string]string{
			"status": "ready",
		})
	} else {
		c.ErrorResponse(503, "服务未就绪")
	}
}

// Live 存活检查（用于Kubernetes）
func (c *HealthController) Live() {
	// 简单的存活检查
	c.SuccessResponse(map[string]string{
		"status": "alive",
	})
}
