@echo off
chcp 65001 >nul
echo === 临时邮箱系统启动脚本 ===

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Go未安装，请先安装Go语言环境
    pause
    exit /b 1
)

echo 📦 安装依赖包...
go mod tidy

if %errorlevel% neq 0 (
    echo ❌ 依赖包安装失败
    pause
    exit /b 1
)

echo 🔧 检查配置文件...
if not exist "conf\app.conf" (
    echo ❌ 配置文件不存在: conf\app.conf
    pause
    exit /b 1
)

echo 🚀 启动临时邮箱系统...
echo.
echo 服务端口：
echo   - Web API: http://localhost:8080
echo   - SMTP:    localhost:2525
echo   - IMAP:    localhost:1143
echo   - POP3:    localhost:1110
echo.
echo 按 Ctrl+C 停止服务
echo.

REM 启动应用
go run main.go

pause
