// 邮箱管理页面JavaScript功能

// 页面状态
let currentEmailAddress = '';
let currentPage = 1;
let currentLimit = 20;
let autoRefreshTimer = null;
let isAutoRefreshEnabled = false;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initMailboxPage();
    bindEvents();
});

// 初始化邮箱页面
function initMailboxPage() {
    console.log('邮箱管理页面初始化');
    
    // 从URL参数获取邮箱地址
    const urlParams = new URLSearchParams(window.location.search);
    const emailFromUrl = urlParams.get('email');
    
    if (emailFromUrl) {
        document.getElementById('emailPrefix').value = emailFromUrl.split('@')[0];
        generateEmailAddress();
    }
    
    // 初始化页面大小选择器
    const pageSizeSelect = document.getElementById('pageSize');
    if (pageSizeSelect) {
        pageSizeSelect.value = currentLimit;
    }
}

// 绑定事件
function bindEvents() {
    // 生成邮箱按钮
    const generateBtn = document.getElementById('generateBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generateEmailAddress);
    }
    
    // 复制按钮
    const copyBtn = document.getElementById('copyBtn');
    if (copyBtn) {
        copyBtn.addEventListener('click', copyEmailAddress);
    }
    
    // 刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshEmails);
    }
    
    // 自动刷新按钮
    const autoRefreshBtn = document.getElementById('autoRefreshBtn');
    if (autoRefreshBtn) {
        autoRefreshBtn.addEventListener('click', toggleAutoRefresh);
    }
    
    // 页面大小选择器
    const pageSizeSelect = document.getElementById('pageSize');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            currentLimit = parseInt(this.value);
            currentPage = 1;
            loadEmails();
        });
    }
    
    // 分页按钮
    const prevPageBtn = document.getElementById('prevPage');
    const nextPageBtn = document.getElementById('nextPage');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
            if (currentPage > 1) {
                currentPage--;
                loadEmails();
            }
        });
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
            currentPage++;
            loadEmails();
        });
    }
    
    // 邮箱输入框回车事件
    const emailInput = document.getElementById('emailPrefix');
    if (emailInput) {
        emailInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generateEmailAddress();
            }
        });
        
        // 输入时实时预览
        emailInput.addEventListener('input', function() {
            const prefix = this.value.trim();
            if (prefix) {
                const fullEmail = `${prefix}@${systemInfo.mail_domain}`;
                // 可以添加实时预览逻辑
            }
        });
    }
    
    // 模态框关闭事件
    const closeModal = document.getElementById('closeModal');
    const emailModal = document.getElementById('emailModal');
    
    if (closeModal && emailModal) {
        closeModal.addEventListener('click', () => {
            emailModal.classList.remove('show');
        });
        
        emailModal.addEventListener('click', (e) => {
            if (e.target === emailModal) {
                emailModal.classList.remove('show');
            }
        });
    }
    
    // 内容标签切换
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            switchContentTab(tabName);
        });
    });
}

// 生成邮箱地址
function generateEmailAddress() {
    const emailInput = document.getElementById('emailPrefix');
    const generatedEmailDiv = document.getElementById('generatedEmail');
    const emailTextSpan = document.getElementById('emailText');
    
    if (!emailInput || !generatedEmailDiv || !emailTextSpan) return;
    
    let prefix = emailInput.value.trim();
    
    // 如果没有输入前缀，生成随机前缀
    if (!prefix) {
        prefix = Utils.generateRandomPrefix();
        emailInput.value = prefix;
    }
    
    // 验证前缀格式
    if (!/^[a-zA-Z0-9._-]+$/.test(prefix)) {
        Utils.showNotification('邮箱前缀只能包含字母、数字、点号、下划线和连字符', 'error');
        return;
    }
    
    const fullEmail = `${prefix}@${systemInfo.mail_domain}`;
    currentEmailAddress = fullEmail;
    
    // 显示生成的邮箱
    emailTextSpan.textContent = fullEmail;
    generatedEmailDiv.style.display = 'block';
    
    // 添加显示动画
    generatedEmailDiv.style.opacity = '0';
    generatedEmailDiv.style.transform = 'translateY(-10px)';
    
    setTimeout(() => {
        generatedEmailDiv.style.transition = 'all 0.3s ease';
        generatedEmailDiv.style.opacity = '1';
        generatedEmailDiv.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动加载邮件
    loadEmails();
    
    Utils.showNotification(`邮箱地址已生成: ${fullEmail}`, 'success');
}

// 复制邮箱地址
function copyEmailAddress() {
    const emailText = document.getElementById('emailText');
    if (emailText) {
        Utils.copyToClipboard(emailText.textContent);
    }
}

// 加载邮件列表
async function loadEmails() {
    if (!currentEmailAddress) {
        return;
    }
    
    Utils.showLoading();
    
    try {
        const response = await API.getEmails(currentEmailAddress, currentPage, currentLimit, false);
        
        if (response.code === 200) {
            displayEmails(response.data.emails || []);
            updatePagination(response.data.total || 0);
            updateEmailCount(response.data.total || 0);
        } else {
            throw new Error(response.message || '获取邮件列表失败');
        }
    } catch (error) {
        console.error('加载邮件失败:', error);
        Utils.showNotification('加载邮件失败: ' + error.message, 'error');
        displayEmptyState();
    } finally {
        Utils.hideLoading();
    }
}

// 显示邮件列表
function displayEmails(emails) {
    const emailList = document.getElementById('emailList');
    const emptyState = document.getElementById('emptyState');
    
    if (!emailList) return;
    
    if (emails.length === 0) {
        displayEmptyState();
        return;
    }
    
    // 隐藏空状态
    if (emptyState) {
        emptyState.style.display = 'none';
    }
    
    // 清空现有内容
    emailList.innerHTML = '';
    
    emails.forEach(email => {
        const emailItem = createEmailItem(email);
        emailList.appendChild(emailItem);
    });
}

// 创建邮件项目元素
function createEmailItem(email) {
    const item = document.createElement('div');
    item.className = 'email-item';
    if (!email.is_read) {
        item.classList.add('unread');
    }
    
    // 生成发件人头像
    const fromName = email.from_name || email.from_address || 'Unknown';
    const avatarText = fromName.charAt(0).toUpperCase();
    
    // 格式化邮件预览
    const preview = email.text_content ? 
        email.text_content.substring(0, 100) + (email.text_content.length > 100 ? '...' : '') : 
        '(无内容预览)';
    
    item.innerHTML = `
        <div class="email-avatar">${avatarText}</div>
        <div class="email-info">
            <div class="email-header">
                <div class="email-from">${fromName}</div>
                <div class="email-time">${Utils.formatTime(email.received_at)}</div>
            </div>
            <div class="email-subject">${email.subject || '(无主题)'}</div>
            <div class="email-preview">${preview}</div>
            <div class="email-meta">
                <span class="email-size">${Utils.formatFileSize(email.size)}</span>
                ${email.attachment_count > 0 ? 
                    `<span class="email-attachments-count">📎 ${email.attachment_count} 个附件</span>` : 
                    ''
                }
            </div>
        </div>
    `;
    
    // 添加点击事件
    item.addEventListener('click', () => {
        showEmailDetail(email.id);
    });
    
    return item;
}

// 显示空状态
function displayEmptyState() {
    const emailList = document.getElementById('emailList');
    const emptyState = document.getElementById('emptyState');
    
    if (emailList && emptyState) {
        emailList.innerHTML = '';
        emailList.appendChild(emptyState);
        emptyState.style.display = 'block';
    }
    
    // 隐藏分页
    const pagination = document.getElementById('pagination');
    if (pagination) {
        pagination.style.display = 'none';
    }
}

// 更新分页信息
function updatePagination(total) {
    const pagination = document.getElementById('pagination');
    const pageInfo = document.getElementById('pageInfo');
    const prevPageBtn = document.getElementById('prevPage');
    const nextPageBtn = document.getElementById('nextPage');
    
    if (!pagination || !pageInfo) return;
    
    const totalPages = Math.ceil(total / currentLimit);
    
    if (totalPages <= 1) {
        pagination.style.display = 'none';
        return;
    }
    
    pagination.style.display = 'flex';
    pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
    
    if (prevPageBtn) {
        prevPageBtn.disabled = currentPage <= 1;
    }
    
    if (nextPageBtn) {
        nextPageBtn.disabled = currentPage >= totalPages;
    }
}

// 更新邮件数量
function updateEmailCount(total) {
    const emailCount = document.getElementById('emailCount');
    if (emailCount) {
        emailCount.textContent = `${total} 封邮件`;
    }
}

// 刷新邮件
function refreshEmails() {
    if (currentEmailAddress) {
        currentPage = 1;
        loadEmails();
        Utils.showNotification('邮件列表已刷新', 'success');
    } else {
        Utils.showNotification('请先生成邮箱地址', 'warning');
    }
}

// 切换自动刷新
function toggleAutoRefresh() {
    const autoRefreshBtn = document.getElementById('autoRefreshBtn');
    
    if (isAutoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshTimer);
        isAutoRefreshEnabled = false;
        autoRefreshBtn.textContent = '⏱️ 自动刷新';
        autoRefreshBtn.classList.remove('btn-primary');
        autoRefreshBtn.classList.add('btn-secondary');
        Utils.showNotification('自动刷新已关闭', 'info');
    } else {
        // 开始自动刷新
        if (!currentEmailAddress) {
            Utils.showNotification('请先生成邮箱地址', 'warning');
            return;
        }
        
        autoRefreshTimer = setInterval(loadEmails, CONFIG.refreshInterval);
        isAutoRefreshEnabled = true;
        autoRefreshBtn.textContent = '⏹️ 停止刷新';
        autoRefreshBtn.classList.remove('btn-secondary');
        autoRefreshBtn.classList.add('btn-primary');
        Utils.showNotification('自动刷新已开启', 'success');
    }
}

// 显示邮件详情
async function showEmailDetail(emailId) {
    if (!currentEmailAddress) return;
    
    Utils.showLoading();
    
    try {
        const response = await API.getEmailDetail(currentEmailAddress, emailId);
        
        if (response.code === 200) {
            const email = response.data;
            displayEmailModal(email);
        } else {
            throw new Error(response.message || '获取邮件详情失败');
        }
    } catch (error) {
        console.error('获取邮件详情失败:', error);
        Utils.showNotification('获取邮件详情失败: ' + error.message, 'error');
    } finally {
        Utils.hideLoading();
    }
}

// 显示邮件详情模态框
function displayEmailModal(email) {
    const modal = document.getElementById('emailModal');
    const emailSubject = document.getElementById('emailSubject');
    const emailFrom = document.getElementById('emailFrom');
    const emailTo = document.getElementById('emailTo');
    const emailTime = document.getElementById('emailTime');
    const emailSize = document.getElementById('emailSize');
    const textContent = document.getElementById('textContent');
    const htmlContent = document.getElementById('htmlContent');
    const rawContent = document.getElementById('rawContent');
    const emailAttachments = document.getElementById('emailAttachments');
    const attachmentsList = document.getElementById('attachmentsList');
    
    if (!modal) return;
    
    // 填充邮件信息
    if (emailSubject) emailSubject.textContent = email.subject || '(无主题)';
    if (emailFrom) emailFrom.textContent = email.from_name ? `${email.from_name} <${email.from_address}>` : email.from_address;
    if (emailTo) emailTo.textContent = email.to_address;
    if (emailTime) emailTime.textContent = new Date(email.received_at).toLocaleString('zh-CN');
    if (emailSize) emailSize.textContent = Utils.formatFileSize(email.size);
    
    // 填充邮件内容
    if (textContent) {
        textContent.innerHTML = `<pre>${email.text_content || '(无纯文本内容)'}</pre>`;
    }
    
    if (htmlContent) {
        if (email.html_content) {
            htmlContent.innerHTML = `<iframe srcdoc="${email.html_content.replace(/"/g, '&quot;')}" style="width: 100%; height: 400px; border: none; border-radius: 6px;"></iframe>`;
        } else {
            htmlContent.innerHTML = '<p style="color: #64748b; font-style: italic;">无HTML内容</p>';
        }
    }
    
    if (rawContent) {
        rawContent.innerHTML = `<pre>${email.raw_content || '(无原始内容)'}</pre>`;
    }
    
    // 处理附件
    if (email.attachments && email.attachments.length > 0) {
        displayAttachments(email.attachments);
        if (emailAttachments) emailAttachments.style.display = 'block';
    } else {
        if (emailAttachments) emailAttachments.style.display = 'none';
    }
    
    // 显示模态框
    modal.classList.add('show');
    
    // 默认显示纯文本内容
    switchContentTab('text');
}

// 显示附件列表
function displayAttachments(attachments) {
    const attachmentsList = document.getElementById('attachmentsList');
    if (!attachmentsList) return;
    
    attachmentsList.innerHTML = '';
    
    attachments.forEach(attachment => {
        const attachmentItem = document.createElement('div');
        attachmentItem.className = 'attachment-item';
        
        // 根据文件类型选择图标
        const icon = getFileIcon(attachment.content_type);
        
        attachmentItem.innerHTML = `
            <div class="attachment-info">
                <span class="attachment-icon">${icon}</span>
                <div class="attachment-details">
                    <div class="attachment-name">${attachment.filename}</div>
                    <div class="attachment-size">${Utils.formatFileSize(attachment.size)}</div>
                </div>
            </div>
            <button class="btn btn-copy attachment-download" onclick="downloadAttachment(${attachment.id}, '${attachment.filename}')">
                下载
            </button>
        `;
        
        attachmentsList.appendChild(attachmentItem);
    });
}

// 获取文件图标
function getFileIcon(contentType) {
    if (contentType.startsWith('image/')) return '🖼️';
    if (contentType.startsWith('video/')) return '🎥';
    if (contentType.startsWith('audio/')) return '🎵';
    if (contentType.includes('pdf')) return '📄';
    if (contentType.includes('word')) return '📝';
    if (contentType.includes('excel')) return '📊';
    if (contentType.includes('powerpoint')) return '📽️';
    if (contentType.includes('zip') || contentType.includes('rar')) return '📦';
    return '📎';
}

// 下载附件
async function downloadAttachment(attachmentId, filename) {
    try {
        const url = `${CONFIG.baseUrl}/emails/${encodeURIComponent(currentEmailAddress)}/attachments/${attachmentId}`;
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        Utils.showNotification(`开始下载: ${filename}`, 'success');
    } catch (error) {
        console.error('下载附件失败:', error);
        Utils.showNotification('下载附件失败: ' + error.message, 'error');
    }
}

// 切换内容标签
function switchContentTab(tabName) {
    // 更新标签按钮状态
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-tab') === tabName) {
            btn.classList.add('active');
        }
    });
    
    // 更新内容显示
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    const targetContent = document.getElementById(tabName + 'Content');
    if (targetContent) {
        targetContent.classList.add('active');
    }
}

// 页面卸载时清理定时器
window.addEventListener('beforeunload', function() {
    if (autoRefreshTimer) {
        clearInterval(autoRefreshTimer);
    }
});

// 导出函数供HTML调用
window.generateEmailAddress = generateEmailAddress;
window.copyEmailAddress = copyEmailAddress;
window.refreshEmails = refreshEmails;
window.toggleAutoRefresh = toggleAutoRefresh;
window.downloadAttachment = downloadAttachment;
