package controllers

import (
	"encoding/json"
	"log"
	"runtime"

	"github.com/beego/beego/v2/server/web"
)

// BaseController 基础控制器
type BaseController struct {
	web.Controller
}

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResponse 成功响应
func (c *BaseController) SuccessResponse(data interface{}) {
	c.Data["json"] = Response{
		Code:    200,
		Message: "success",
		Data:    data,
	}
	c.ServeJSON()
}

// ErrorResponse 错误响应
func (c *BaseController) ErrorResponse(code int, message string) {
	// 记录错误日志
	if code >= 500 {
		// 获取调用栈信息
		_, file, line, ok := runtime.Caller(1)
		if ok {
			log.Printf("服务器错误 [%d]: %s (位置: %s:%d)", code, message, file, line)
		} else {
			log.Printf("服务器错误 [%d]: %s", code, message)
		}
	}

	c.Ctx.ResponseWriter.WriteHeader(code)
	c.Data["json"] = Response{
		Code:    code,
		Message: message,
	}
	c.ServeJSON()
}

// InternalErrorResponse 内部服务器错误响应
func (c *BaseController) InternalErrorResponse(err error) {
	log.Printf("内部服务器错误: %v", err)
	c.ErrorResponse(500, "内部服务器错误")
}

// BadRequestResponse 请求参数错误响应
func (c *BaseController) BadRequestResponse(message string) {
	c.ErrorResponse(400, message)
}

// NotFoundResponse 资源不存在响应
func (c *BaseController) NotFoundResponse(message string) {
	c.ErrorResponse(404, message)
}

// ParseJSON 解析JSON请求体（带错误处理）
func (c *BaseController) ParseJSON(v interface{}) error {
	if err := json.Unmarshal(c.Ctx.Input.RequestBody, v); err != nil {
		log.Printf("JSON解析失败: %v, 请求体: %s", err, string(c.Ctx.Input.RequestBody))
		return err
	}
	return nil
}



// Health 健康检查
func (c *BaseController) Health() {
	c.SuccessResponse(map[string]string{
		"status": "ok",
		"service": "temp-email-system",
	})
}
