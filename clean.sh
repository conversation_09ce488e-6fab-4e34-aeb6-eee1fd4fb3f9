#!/bin/bash

echo "=== Temporary Email System - Project Cleanup ==="

echo "Cleaning build artifacts..."

# Remove executable files
rm -f temp-email-system.exe
rm -f temp-email-system

# Remove build directories
rm -rf build/
rm -rf dist/
rm -rf release/

# Remove release packages
rm -f *.zip
rm -f *.tar.gz
rm -f *.tar.bz2

# Remove log files
rm -f *.log
rm -rf logs/

# Remove temporary files
rm -f *.tmp
rm -f *.temp
rm -f *.bak
rm -f *.backup

# Remove test files
rm -f *.test
rm -f coverage.txt
rm -f coverage.html
rm -f *.cover

# Remove Go cache and mod cache (optional)
# go clean -cache
# go clean -modcache

echo ""
echo "Project cleanup completed!"
echo ""
echo "Cleaned items:"
echo "  - Executable files"
echo "  - Build directories"
echo "  - Release packages"
echo "  - Log files"
echo "  - Temporary files"
echo "  - Test artifacts"
echo ""
