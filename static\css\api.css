/* API文档页面样式 */

.api-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
    margin-top: 40px;
}

/* 侧边栏 */
.api-sidebar {
    background-color: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e5e7eb;
    height: fit-content;
    position: sticky;
    top: 100px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.api-sidebar h3 {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 20px;
}

.api-nav {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.nav-group h4 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    display: block;
    padding: 8px 12px;
    color: #64748b;
    text-decoration: none;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-bottom: 4px;
}

.nav-item:hover {
    background-color: #f1f5f9;
    color: #2563eb;
}

.nav-item.active {
    background-color: #dbeafe;
    color: #2563eb;
    font-weight: 500;
}

/* API内容区域 */
.api-content {
    background-color: white;
    border-radius: 12px;
    padding: 32px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.api-header {
    margin-bottom: 40px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
}

.api-header h2 {
    font-size: 32px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
}

.api-header p {
    color: #64748b;
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.api-base-url {
    display: flex;
    align-items: center;
    gap: 12px;
    background-color: #f8fafc;
    padding: 16px 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.api-base-url label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.api-base-url code {
    background-color: #1e293b;
    color: #10b981;
    padding: 6px 12px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    font-weight: 500;
}

/* API接口区域 */
.api-section {
    margin-bottom: 48px;
    padding-bottom: 32px;
    border-bottom: 1px solid #f1f5f9;
}

.api-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.api-section h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 16px;
}

.api-endpoint {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 16px 20px;
    background-color: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.method {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.method.get {
    background-color: #dcfce7;
    color: #166534;
}

.method.post {
    background-color: #dbeafe;
    color: #1e40af;
}

.method.put {
    background-color: #fef3c7;
    color: #92400e;
}

.method.delete {
    background-color: #fecaca;
    color: #991b1b;
}

.api-endpoint code {
    background-color: #1e293b;
    color: #10b981;
    padding: 8px 12px;
    border-radius: 6px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    font-weight: 500;
}

.api-description {
    margin-bottom: 24px;
}

.api-description p {
    color: #64748b;
    line-height: 1.6;
}

/* 参数表格 */
.api-params {
    margin-bottom: 32px;
}

.api-params h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 20px 0 12px;
}

.params-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.params-table th {
    background-color: #f8fafc;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    border-bottom: 1px solid #e5e7eb;
}

.params-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    color: #64748b;
    font-size: 13px;
}

.params-table tr:last-child td {
    border-bottom: none;
}

.params-table code {
    background-color: #f1f5f9;
    color: #374151;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

/* 在线测试区域 */
.api-test {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    margin-top: 24px;
}

.api-test h4 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 20px;
}

.test-form {
    margin-bottom: 24px;
}

.test-form .form-group {
    margin-bottom: 16px;
}

.test-form label {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
    font-size: 14px;
}

.test-form .form-input {
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
}

.test-form input[type="checkbox"] {
    margin-right: 8px;
}

.test-result {
    margin-top: 20px;
}

.test-result h5 {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.result-code {
    background-color: #1e293b;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    max-height: 300px;
    overflow-y: auto;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.result-code:empty::before {
    content: "点击"发送请求"按钮查看响应结果...";
    color: #64748b;
    font-style: italic;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .api-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .api-sidebar {
        position: static;
        order: 2;
    }
    
    .api-content {
        order: 1;
    }
}

@media (max-width: 768px) {
    .api-content {
        padding: 20px;
    }
    
    .api-header h2 {
        font-size: 24px;
    }
    
    .api-section h3 {
        font-size: 20px;
    }
    
    .api-endpoint {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .api-endpoint code {
        word-break: break-all;
    }
    
    .params-table {
        font-size: 12px;
    }
    
    .params-table th,
    .params-table td {
        padding: 8px 12px;
    }
    
    .test-form {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
}
