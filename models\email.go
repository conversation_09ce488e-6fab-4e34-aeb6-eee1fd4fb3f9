package models

import (
	"encoding/base64"
	"fmt"
	"log"
	"time"

	"github.com/beego/beego/v2/client/orm"
	"github.com/go-redis/redis/v8"
	"goemail/services/cache"
)

// Email 邮件模型
type Email struct {
	Id          int64        `orm:"auto;pk" json:"id"`
	MessageId   string       `orm:"size(255);unique" json:"message_id"`
	ToAddress   string       `orm:"size(255);index" json:"to_address"`
	FromAddress string       `orm:"size(255)" json:"from_address"`
	Subject     string       `orm:"type(text)" json:"subject"`
	BodyText    string       `orm:"type(text)" json:"body_text"`
	BodyHtml    string       `orm:"type(text)" json:"body_html"`
	RawContent  string       `orm:"type(longtext)" json:"raw_content"`
	ReceivedAt  time.Time    `orm:"auto_now_add;type(datetime);index" json:"received_at"`
	Size        int          `json:"size"`
	Attachments []*Attachment `orm:"reverse(many)" json:"attachments,omitempty"`
}

// Attachment 附件模型
type Attachment struct {
	Id          int64  `orm:"auto;pk" json:"id"`
	Email       *Email `orm:"rel(fk)" json:"-"`
	Filename    string `orm:"size(255)" json:"filename"`
	ContentType string `orm:"size(100)" json:"content_type"`
	Size        int    `json:"size"`
	Content     string `orm:"type(longtext)" json:"-"` // 改为string类型存储base64编码的内容
}

// TableName 设置表名
func (e *Email) TableName() string {
	return "emails"
}

func (a *Attachment) TableName() string {
	return "attachments"
}

// SetContentBytes 设置附件内容（将字节数组编码为base64字符串）
func (a *Attachment) SetContentBytes(content []byte) {
	a.Content = base64.StdEncoding.EncodeToString(content)
	a.Size = len(content)
}

// GetContentBytes 获取附件内容（将base64字符串解码为字节数组）
func (a *Attachment) GetContentBytes() ([]byte, error) {
	if a.Content == "" {
		return []byte{}, nil
	}
	return base64.StdEncoding.DecodeString(a.Content)
}

// EmailManager 邮件管理器
type EmailManager struct{
	redisService *cache.RedisService
	keyGenerator *cache.CacheKeyGenerator
}

// NewEmailManager 创建邮件管理器
func NewEmailManager() *EmailManager {
	return &EmailManager{
		redisService: cache.GetRedisService(),
		keyGenerator: cache.NewCacheKeyGenerator(),
	}
}

// GetEmailsByAddress 根据邮箱地址获取邮件列表（不包含邮件内容，用于列表显示）
func (em *EmailManager) GetEmailsByAddress(address string, limit, offset int) ([]*Email, int64, error) {
	// 参数验证
	if address == "" {
		return nil, 0, fmt.Errorf("邮箱地址不能为空")
	}
	if limit <= 0 || limit > 1000 {
		limit = 20 // 设置合理的默认值和上限
	}
	if offset < 0 {
		offset = 0
	}

	page := (offset / limit) + 1

	// 尝试从缓存获取
	cacheKey := em.keyGenerator.EmailListKey(address, page, limit, false)
	var cachedResult struct {
		Emails []*Email `json:"emails"`
		Total  int64    `json:"total"`
	}

	err := em.redisService.Get(cacheKey, &cachedResult)
	if err == nil {
		log.Printf("邮件列表缓存命中: %s", cacheKey)
		return cachedResult.Emails, cachedResult.Total, nil
	}

	// 缓存未命中，从数据库查询
	if err != redis.Nil {
		log.Printf("获取邮件列表缓存失败: %v", err)
	}

	return em.getEmailsFromDB(address, limit, offset, false)
}

// getEmailsFromDB 从数据库获取邮件列表（内部方法）
func (em *EmailManager) getEmailsFromDB(address string, limit, offset int, includeContent bool) ([]*Email, int64, error) {
	o := orm.NewOrm()

	var emails []*Email
	qs := o.QueryTable("emails").Filter("to_address", address).OrderBy("-received_at")

	// 获取总数
	total, err := qs.Count()
	if err != nil {
		return nil, 0, fmt.Errorf("查询邮件总数失败: %v", err)
	}

	// 分页查询
	if includeContent {
		_, err = qs.Limit(limit, offset).All(&emails)
	} else {
		// 排除大字段以提高性能
		_, err = qs.Limit(limit, offset).Exclude("raw_content", "body_html", "body_text").All(&emails)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("查询邮件列表失败: %v", err)
	}

	// 如果包含内容，加载附件信息
	if includeContent {
		for _, email := range emails {
			if _, err := o.LoadRelated(email, "Attachments"); err != nil {
				log.Printf("加载邮件 %d 的附件失败: %v", email.Id, err)
				// 不中断处理，继续处理其他邮件
			}
		}
	}

	// 存入缓存
	em.cacheEmailList(address, limit, offset, includeContent, emails, total)

	return emails, total, nil
}

// cacheEmailList 缓存邮件列表
func (em *EmailManager) cacheEmailList(address string, limit, offset int, includeContent bool, emails []*Email, total int64) {
	page := (offset / limit) + 1
	cacheKey := em.keyGenerator.EmailListKey(address, page, limit, includeContent)

	cacheData := struct {
		Emails []*Email `json:"emails"`
		Total  int64    `json:"total"`
	}{
		Emails: emails,
		Total:  total,
	}

	ttl := cache.GetCacheTTL("email_list")
	if err := em.redisService.Set(cacheKey, cacheData, ttl); err != nil {
		log.Printf("设置邮件列表缓存失败: %v", err)
	}
}

// GetEmailsWithContentByAddress 根据邮箱地址获取邮件列表（包含完整内容）
func (em *EmailManager) GetEmailsWithContentByAddress(address string, limit, offset int) ([]*Email, int64, error) {
	// 参数验证
	if address == "" {
		return nil, 0, fmt.Errorf("邮箱地址不能为空")
	}
	if limit <= 0 || limit > 100 { // 包含内容的查询限制更严格
		limit = 10
	}
	if offset < 0 {
		offset = 0
	}

	page := (offset / limit) + 1

	// 尝试从缓存获取
	cacheKey := em.keyGenerator.EmailListKey(address, page, limit, true)
	var cachedResult struct {
		Emails []*Email `json:"emails"`
		Total  int64    `json:"total"`
	}

	err := em.redisService.Get(cacheKey, &cachedResult)
	if err == nil {
		log.Printf("邮件列表（含内容）缓存命中: %s", cacheKey)
		return cachedResult.Emails, cachedResult.Total, nil
	}

	// 缓存未命中，从数据库查询
	if err != redis.Nil {
		log.Printf("获取邮件列表（含内容）缓存失败: %v", err)
	}

	return em.getEmailsFromDB(address, limit, offset, true)
}

// GetEmailById 根据ID获取邮件详情
func (em *EmailManager) GetEmailById(id int64) (*Email, error) {
	// 首先获取邮件基本信息以确定邮箱地址
	o := orm.NewOrm()
	tempEmail := &Email{Id: id}
	err := o.Read(tempEmail, "Id")
	if err != nil {
		return nil, err
	}

	// 尝试从缓存获取
	cacheKey := em.keyGenerator.EmailDetailKey(tempEmail.ToAddress, id)
	var cachedEmail Email

	err = em.redisService.Get(cacheKey, &cachedEmail)
	if err == nil {
		log.Printf("邮件详情缓存命中: %s", cacheKey)
		return &cachedEmail, nil
	}

	// 缓存未命中，从数据库查询
	if err != redis.Nil {
		log.Printf("获取邮件详情缓存失败: %v", err)
	}

	email := &Email{Id: id}
	err = o.Read(email)
	if err != nil {
		return nil, err
	}

	// 加载附件
	_, err = o.LoadRelated(email, "Attachments")
	if err != nil {
		return nil, err
	}

	// 存入缓存
	ttl := cache.GetCacheTTL("email_detail")
	if err := em.redisService.Set(cacheKey, email, ttl); err != nil {
		log.Printf("设置邮件详情缓存失败: %v", err)
	}

	return email, nil
}

// SaveEmail 保存邮件（支持事务）
func (em *EmailManager) SaveEmail(email *Email) error {
	if email == nil {
		return fmt.Errorf("邮件对象不能为空")
	}

	// 验证必要字段
	if email.MessageId == "" {
		return fmt.Errorf("邮件MessageId不能为空")
	}
	if email.ToAddress == "" {
		return fmt.Errorf("收件人地址不能为空")
	}

	o := orm.NewOrm()

	// 检查是否已存在相同MessageId的邮件
	exist := o.QueryTable("emails").Filter("message_id", email.MessageId).Exist()
	if exist {
		log.Printf("邮件已存在，跳过保存: %s", email.MessageId)
		return nil // 邮件已存在，不重复保存
	}

	// 直接保存邮件（Beego ORM的事务处理较复杂，这里简化处理）
	_, err := o.Insert(email)
	if err != nil {
		return fmt.Errorf("保存邮件失败: %v", err)
	}

	// 异步清除相关缓存
	go func() {
		em.clearEmailListCache(email.ToAddress)
	}()

	log.Printf("邮件保存成功: %s -> %s", email.FromAddress, email.ToAddress)
	return nil
}

// SaveAttachment 保存附件
func (em *EmailManager) SaveAttachment(attachment *Attachment) error {
	o := orm.NewOrm()
	_, err := o.Insert(attachment)
	return err
}

// DeleteEmail 删除邮件
func (em *EmailManager) DeleteEmail(id int64) error {
	o := orm.NewOrm()
	email := &Email{Id: id}
	_, err := o.Delete(email)
	return err
}

// GetAttachment 获取附件内容
func (em *EmailManager) GetAttachment(id int64) (*Attachment, error) {
	o := orm.NewOrm()

	attachment := &Attachment{Id: id}
	err := o.Read(attachment)
	if err != nil {
		return nil, err
	}

	return attachment, nil
}

// clearEmailListCache 清除邮件列表缓存
func (em *EmailManager) clearEmailListCache(address string) {
	// 清除邮件列表缓存（所有分页和模式）
	pattern := em.keyGenerator.EmailListPattern(address)
	if err := em.redisService.DeletePattern(pattern); err != nil {
		log.Printf("清除邮件列表缓存失败: %v", err)
	}

	// 清除邮件统计缓存
	statsKey := em.keyGenerator.EmailStatsKey(address)
	if err := em.redisService.Delete(statsKey); err != nil {
		log.Printf("清除邮件统计缓存失败: %v", err)
	}
}

// ClearEmailDetailCache 清除单封邮件详情缓存
func (em *EmailManager) ClearEmailDetailCache(address string, emailId int64) error {
	cacheKey := em.keyGenerator.EmailDetailKey(address, emailId)
	return em.redisService.Delete(cacheKey)
}

// ClearAllEmailCache 清除指定邮箱的所有缓存
func (em *EmailManager) ClearAllEmailCache(address string) error {
	pattern := em.keyGenerator.AllEmailPattern(address)
	return em.redisService.DeletePattern(pattern)
}

// WarmupCache 预热缓存
func (em *EmailManager) WarmupCache(address string) error {
	log.Printf("开始预热邮箱缓存: %s", address)

	// 预热第一页邮件列表（基本信息）
	_, _, err := em.GetEmailsByAddress(address, 20, 0)
	if err != nil {
		log.Printf("预热邮件列表缓存失败: %v", err)
		return err
	}

	// 预热第一页邮件列表（完整内容）
	_, _, err = em.GetEmailsWithContentByAddress(address, 10, 0)
	if err != nil {
		log.Printf("预热邮件列表（含内容）缓存失败: %v", err)
		return err
	}

	log.Printf("邮箱缓存预热完成: %s", address)
	return nil
}

// GetCacheStats 获取缓存统计信息
func (em *EmailManager) GetCacheStats() *cache.CacheStats {
	return em.redisService.GetStats()
}

// ResetCacheStats 重置缓存统计信息
func (em *EmailManager) ResetCacheStats() {
	em.redisService.ResetStats()
}
