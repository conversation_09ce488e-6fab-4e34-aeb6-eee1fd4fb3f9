package cache

import (
	"fmt"
	"strconv"
	"strings"
)

// CacheKeyGenerator 缓存键生成器
type CacheKeyGenerator struct{}

// NewCacheKeyGenerator 创建缓存键生成器
func NewCacheKeyGenerator() *CacheKeyGenerator {
	return &CacheKeyGenerator{}
}

// EmailListKey 生成邮件列表缓存键
// 格式: email:list:{address}:{page}:{limit}:{include_content}
func (c *CacheKeyGenerator) EmailListKey(address string, page, limit int, includeContent bool) string {
	includeContentStr := "false"
	if includeContent {
		includeContentStr = "true"
	}
	
	// 清理邮箱地址，确保键的安全性
	cleanAddress := c.cleanAddress(address)
	
	return fmt.Sprintf("email:list:%s:%d:%d:%s", 
		cleanAddress, page, limit, includeContentStr)
}

// EmailDetailKey 生成单封邮件缓存键
// 格式: email:detail:{address}:{id}
func (c *CacheKeyGenerator) EmailDetail<PERSON>ey(address string, emailId int64) string {
	cleanAddress := c.cleanAddress(address)
	return fmt.Sprintf("email:detail:%s:%d", cleanAddress, emailId)
}

// EmailStatsKey 生成邮件统计缓存键
// 格式: email:stats:{address}
func (c *CacheKeyGenerator) EmailStatsKey(address string) string {
	cleanAddress := c.cleanAddress(address)
	return fmt.Sprintf("email:stats:%s", cleanAddress)
}

// EmailListPattern 生成邮件列表缓存模式（用于批量删除）
// 格式: email:list:{address}:*
func (c *CacheKeyGenerator) EmailListPattern(address string) string {
	cleanAddress := c.cleanAddress(address)
	return fmt.Sprintf("email:list:%s:*", cleanAddress)
}

// EmailDetailPattern 生成邮件详情缓存模式（用于批量删除）
// 格式: email:detail:{address}:*
func (c *CacheKeyGenerator) EmailDetailPattern(address string) string {
	cleanAddress := c.cleanAddress(address)
	return fmt.Sprintf("email:detail:%s:*", cleanAddress)
}

// AllEmailPattern 生成所有邮件相关缓存模式
// 格式: email:*:{address}:*
func (c *CacheKeyGenerator) AllEmailPattern(address string) string {
	cleanAddress := c.cleanAddress(address)
	return fmt.Sprintf("email:*:%s:*", cleanAddress)
}

// cleanAddress 清理邮箱地址，确保可以安全用作Redis键
func (c *CacheKeyGenerator) cleanAddress(address string) string {
	// 转换为小写
	address = strings.ToLower(address)
	
	// 替换特殊字符
	replacements := map[string]string{
		" ":  "_",
		":":  "_",
		"*":  "_",
		"?":  "_",
		"[":  "_",
		"]":  "_",
		"{":  "_",
		"}":  "_",
		"\\": "_",
		"|":  "_",
	}
	
	for old, new := range replacements {
		address = strings.ReplaceAll(address, old, new)
	}
	
	return address
}

// ParseEmailListKey 解析邮件列表缓存键
func (c *CacheKeyGenerator) ParseEmailListKey(key string) (address string, page, limit int, includeContent bool, err error) {
	parts := strings.Split(key, ":")
	if len(parts) != 6 || parts[0] != "email" || parts[1] != "list" {
		return "", 0, 0, false, fmt.Errorf("invalid email list cache key format")
	}
	
	address = parts[2]
	
	page, err = strconv.Atoi(parts[3])
	if err != nil {
		return "", 0, 0, false, fmt.Errorf("invalid page number in cache key")
	}
	
	limit, err = strconv.Atoi(parts[4])
	if err != nil {
		return "", 0, 0, false, fmt.Errorf("invalid limit number in cache key")
	}
	
	includeContent = parts[5] == "true"
	
	return address, page, limit, includeContent, nil
}

// ParseEmailDetailKey 解析邮件详情缓存键
func (c *CacheKeyGenerator) ParseEmailDetailKey(key string) (address string, emailId int64, err error) {
	parts := strings.Split(key, ":")
	if len(parts) != 4 || parts[0] != "email" || parts[1] != "detail" {
		return "", 0, fmt.Errorf("invalid email detail cache key format")
	}
	
	address = parts[2]
	
	emailId, err = strconv.ParseInt(parts[3], 10, 64)
	if err != nil {
		return "", 0, fmt.Errorf("invalid email ID in cache key")
	}
	
	return address, emailId, nil
}

// ValidateKey 验证缓存键格式
func (c *CacheKeyGenerator) ValidateKey(key string) bool {
	parts := strings.Split(key, ":")
	if len(parts) < 3 {
		return false
	}
	
	// 检查前缀
	if parts[0] != "email" {
		return false
	}
	
	// 检查类型
	validTypes := []string{"list", "detail", "stats"}
	typeValid := false
	for _, validType := range validTypes {
		if parts[1] == validType {
			typeValid = true
			break
		}
	}
	
	return typeValid
}

// GetKeyInfo 获取缓存键信息
func (c *CacheKeyGenerator) GetKeyInfo(key string) map[string]interface{} {
	info := map[string]interface{}{
		"key":   key,
		"valid": false,
	}
	
	if !c.ValidateKey(key) {
		return info
	}
	
	parts := strings.Split(key, ":")
	info["valid"] = true
	info["type"] = parts[1]
	info["address"] = parts[2]
	
	switch parts[1] {
	case "list":
		if len(parts) >= 5 {
			if page, err := strconv.Atoi(parts[3]); err == nil {
				info["page"] = page
			}
			if limit, err := strconv.Atoi(parts[4]); err == nil {
				info["limit"] = limit
			}
			if len(parts) >= 6 {
				info["include_content"] = parts[5] == "true"
			}
		}
	case "detail":
		if len(parts) >= 4 {
			if emailId, err := strconv.ParseInt(parts[3], 10, 64); err == nil {
				info["email_id"] = emailId
			}
		}
	}
	
	return info
}

// GenerateBatchKeys 批量生成缓存键
func (c *CacheKeyGenerator) GenerateBatchKeys(address string, emailIds []int64) []string {
	keys := make([]string, len(emailIds))
	for i, id := range emailIds {
		keys[i] = c.EmailDetailKey(address, id)
	}
	return keys
}

// GetCacheKeysByPattern 根据模式获取所有匹配的缓存键
func (c *CacheKeyGenerator) GetCacheKeysByPattern(pattern string) []string {
	redis := GetRedisService()
	if !redis.IsEnabled() {
		return []string{}
	}
	
	keys, err := redis.client.Keys(redis.ctx, pattern).Result()
	if err != nil {
		return []string{}
	}
	
	return keys
}
