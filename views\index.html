<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱系统 - Temporary Email System</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/index.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>📧 临时邮箱系统</h1>
                <span class="version">v1.0.0</span>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link active">首页</a>
                <a href="/mailbox" class="nav-link">邮箱管理</a>
                <a href="/api" class="nav-link">API文档</a>
                <a href="/config" class="nav-link">配置说明</a>
            </div>
            <div class="nav-status">
                <span class="status-indicator" id="statusIndicator">
                    <span class="status-dot"></span>
                    <span class="status-text">检查中...</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <!-- 欢迎区域 -->
        <section class="hero-section">
            <div class="hero-content">
                <h2 class="hero-title">无需注册的临时邮箱服务</h2>
                <p class="hero-description">
                    快速创建临时邮箱地址，接收邮件无需注册。支持SMTP、IMAP、POP3协议，
                    提供完整的REST API接口，适用于开发测试和临时邮件接收场景。
                </p>
                <div class="hero-actions">
                    <a href="/mailbox" class="btn btn-primary">开始使用邮箱</a>
                    <a href="/api" class="btn btn-secondary">查看API文档</a>
                </div>
            </div>
        </section>

        <!-- 功能特性 -->
        <section class="features-section">
            <div class="container">
                <h3 class="section-title">核心特性</h3>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h4>无需注册</h4>
                        <p>直接使用任意邮箱地址接收邮件，无需注册或验证过程</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📨</div>
                        <h4>多协议支持</h4>
                        <p>支持SMTP、IMAP、POP3协议，兼容各种邮件客户端</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h4>高性能缓存</h4>
                        <p>Redis缓存机制，显著提升邮件查询和响应速度</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔧</div>
                        <h4>REST API</h4>
                        <p>完整的REST API接口，支持邮件管理和系统监控</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📎</div>
                        <h4>附件支持</h4>
                        <p>完整的邮件附件存储、查看和下载功能</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h4>实时监控</h4>
                        <p>健康检查、性能指标、缓存统计等监控功能</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 快速开始 -->
        <section class="quickstart-section">
            <div class="container">
                <h3 class="section-title">快速开始</h3>
                <div class="quickstart-grid">
                    <div class="quickstart-card">
                        <div class="step-number">1</div>
                        <h4>选择邮箱地址</h4>
                        <p>输入您想要的邮箱前缀，系统会自动生成完整的邮箱地址</p>
                        <div class="example">
                            <code>test123@<span id="domainName">yourdomain.com</span></code>
                        </div>
                    </div>
                    <div class="quickstart-card">
                        <div class="step-number">2</div>
                        <h4>配置邮件客户端</h4>
                        <p>使用生成的邮箱地址配置您的邮件客户端，密码可以是任意字符串</p>
                        <div class="example">
                            <code>密码: 123456 (任意)</code>
                        </div>
                    </div>
                    <div class="quickstart-card">
                        <div class="step-number">3</div>
                        <h4>开始接收邮件</h4>
                        <p>立即开始接收发送到该邮箱地址的邮件，支持实时查看和管理</p>
                        <div class="example">
                            <code>✅ 即时可用</code>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 系统状态 -->
        <section class="status-section">
            <div class="container">
                <h3 class="section-title">系统状态</h3>
                <div class="status-grid" id="systemStatus">
                    <div class="status-card">
                        <div class="status-icon">🌐</div>
                        <h4>Web服务</h4>
                        <div class="status-value" id="webStatus">检查中...</div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">📧</div>
                        <h4>SMTP服务</h4>
                        <div class="status-value" id="smtpStatus">检查中...</div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">📥</div>
                        <h4>IMAP服务</h4>
                        <div class="status-value" id="imapStatus">检查中...</div>
                    </div>
                    <div class="status-card">
                        <div class="status-icon">📤</div>
                        <h4>POP3服务</h4>
                        <div class="status-value" id="pop3Status">检查中...</div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 临时邮箱系统. 基于Go语言和Beego框架开发.</p>
        </div>
    </footer>

    <script src="/static/js/common.js"></script>
    <script src="/static/js/index.js"></script>
</body>
</html>
