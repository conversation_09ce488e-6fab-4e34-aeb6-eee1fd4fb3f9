// 通用JavaScript功能

// 全局配置
const CONFIG = {
    baseUrl: '/api/v1',
    systemInfoUrl: '/system/info',
    healthUrl: '/health',
    refreshInterval: 30000, // 30秒
    autoRefreshEnabled: false
};

// 系统信息
let systemInfo = {
    mail_domain: 'yourdomain.com',
    smtp_port: '465',
    imap_port: '993',
    pop3_port: '995',
    http_port: '8080'
};

// 工具函数
const Utils = {
    // 显示加载指示器
    showLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'flex';
        }
    },

    // 隐藏加载指示器
    hideLoading() {
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'none';
        }
    },

    // 显示通知消息
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.getElementById('notification');
        if (!notification) return;

        notification.textContent = message;
        notification.className = `notification ${type} show`;

        setTimeout(() => {
            notification.classList.remove('show');
        }, duration);
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 格式化时间
    formatTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('已复制到剪贴板', 'success');
        } catch (err) {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showNotification('已复制到剪贴板', 'success');
        }
    },

    // 验证邮箱地址
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    // 生成随机邮箱前缀
    generateRandomPrefix() {
        const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
};

// API请求封装
const API = {
    // 通用请求方法
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };

        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    },

    // GET请求
    async get(endpoint, params = {}) {
        const url = new URL(CONFIG.baseUrl + endpoint, window.location.origin);
        Object.keys(params).forEach(key => {
            if (params[key] !== undefined && params[key] !== null) {
                url.searchParams.append(key, params[key]);
            }
        });
        
        return this.request(url.toString());
    },

    // POST请求
    async post(endpoint, data = {}) {
        return this.request(CONFIG.baseUrl + endpoint, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    },

    // 获取系统信息
    async getSystemInfo() {
        try {
            const data = await this.request(CONFIG.systemInfoUrl);
            if (data.code === 200) {
                return data.data;
            }
            throw new Error(data.message || '获取系统信息失败');
        } catch (error) {
            console.error('获取系统信息失败:', error);
            return systemInfo; // 返回默认值
        }
    },

    // 健康检查
    async healthCheck() {
        try {
            const data = await this.request(CONFIG.healthUrl);
            return data;
        } catch (error) {
            console.error('健康检查失败:', error);
            return { status: 'error', message: error.message };
        }
    },

    // 获取邮件列表
    async getEmails(emailAddress, page = 1, limit = 20, includeContent = false) {
        return this.get(`/emails/${encodeURIComponent(emailAddress)}`, {
            page,
            limit,
            include_content: includeContent
        });
    },

    // 获取邮件详情
    async getEmailDetail(emailAddress, emailId) {
        return this.get(`/emails/${encodeURIComponent(emailAddress)}/${emailId}`);
    },

    // 获取缓存统计
    async getCacheStats() {
        return this.get('/cache/stats');
    },

    // 清除缓存
    async clearCache(pattern = '') {
        return this.post('/cache/clear', { pattern });
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', async function() {
    // 加载系统信息
    await loadSystemInfo();
    
    // 更新系统状态
    await updateSystemStatus();
    
    // 设置定时更新
    setInterval(updateSystemStatus, 30000);
    
    // 初始化导航
    initNavigation();
});

// 加载系统信息
async function loadSystemInfo() {
    try {
        const info = await API.getSystemInfo();
        systemInfo = { ...systemInfo, ...info };
        
        // 更新页面中的域名占位符
        updateDomainPlaceholders();
        
        console.log('系统信息加载成功:', systemInfo);
    } catch (error) {
        console.error('加载系统信息失败:', error);
    }
}

// 更新域名占位符
function updateDomainPlaceholders() {
    const domainElements = document.querySelectorAll('#domainName, .domain-placeholder');
    domainElements.forEach(el => {
        el.textContent = systemInfo.mail_domain;
    });
    
    const serverElements = document.querySelectorAll('.server-placeholder');
    serverElements.forEach(el => {
        el.textContent = systemInfo.mail_domain;
    });
    
    const portElements = document.querySelectorAll('.port-smtp');
    portElements.forEach(el => {
        el.textContent = systemInfo.smtp_port;
    });
    
    const imapPortElements = document.querySelectorAll('.port-imap');
    imapPortElements.forEach(el => {
        el.textContent = systemInfo.imap_port;
    });
    
    const pop3PortElements = document.querySelectorAll('.port-pop3');
    pop3PortElements.forEach(el => {
        el.textContent = systemInfo.pop3_port;
    });
    
    const httpPortElements = document.querySelectorAll('.port-http');
    httpPortElements.forEach(el => {
        el.textContent = systemInfo.http_port;
    });
    
    // 更新base URL
    const baseUrlElement = document.getElementById('baseUrl');
    if (baseUrlElement) {
        baseUrlElement.textContent = `http://localhost:${systemInfo.http_port}/api/v1`;
    }
}

// 更新系统状态
async function updateSystemStatus() {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusDot = statusIndicator?.querySelector('.status-dot');
    const statusText = statusIndicator?.querySelector('.status-text');
    
    try {
        const health = await API.healthCheck();
        
        if (health.status === 'ok') {
            statusDot?.classList.add('online');
            statusDot?.classList.remove('offline');
            statusText && (statusText.textContent = '系统正常');
            
            // 更新各服务状态
            updateServiceStatus('webStatus', '正常', 'online');
            updateServiceStatus('smtpStatus', '正常', 'online');
            updateServiceStatus('imapStatus', '正常', 'online');
            updateServiceStatus('pop3Status', '正常', 'online');
        } else {
            throw new Error(health.message || '系统异常');
        }
    } catch (error) {
        statusDot?.classList.add('offline');
        statusDot?.classList.remove('online');
        statusText && (statusText.textContent = '系统异常');
        
        // 更新各服务状态
        updateServiceStatus('webStatus', '异常', 'offline');
        updateServiceStatus('smtpStatus', '未知', 'offline');
        updateServiceStatus('imapStatus', '未知', 'offline');
        updateServiceStatus('pop3Status', '未知', 'offline');
    }
}

// 更新服务状态
function updateServiceStatus(elementId, status, className) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = status;
        element.className = `status-value ${className}`;
    }
}

// 初始化导航
function initNavigation() {
    // 高亮当前页面
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 导出全局对象
window.Utils = Utils;
window.API = API;
window.CONFIG = CONFIG;
window.systemInfo = systemInfo;
