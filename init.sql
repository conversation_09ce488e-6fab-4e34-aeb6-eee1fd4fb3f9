-- 初始化数据库脚本
CREATE DATABASE IF NOT EXISTS temp_email CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE temp_email;

-- 创建邮件表
CREATE TABLE IF NOT EXISTS emails (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(255) UNIQUE NOT NULL,
    to_address VARCHAR(255) NOT NULL,
    from_address VARCHAR(255) NOT NULL,
    subject TEXT,
    body_text TEXT,
    body_html TEXT,
    raw_content LONGTEXT,
    received_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    size INT DEFAULT 0,
    INDEX idx_to_address (to_address),
    INDEX idx_received_at (received_at),
    INDEX idx_message_id (message_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建附件表
CREATE TABLE IF NOT EXISTS attachments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email_id BIGINT,
    filename VARCHAR(255),
    content_type VARCHAR(100),
    size INT DEFAULT 0,
    content LONGBLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE,
    INDEX idx_email_id (email_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入测试数据
INSERT INTO emails (message_id, to_address, from_address, subject, body_text, body_html, raw_content, size) VALUES
('test-001@temp-email', '<EMAIL>', '<EMAIL>', '欢迎使用临时邮箱系统', 
 '这是一封测试邮件，用于演示临时邮箱系统的功能。\n\n系统特性：\n- 无需注册\n- 支持SMTP/IMAP/POP3\n- REST API接口\n- 附件支持', 
 '<h1>欢迎使用临时邮箱系统</h1><p>这是一封测试邮件，用于演示临时邮箱系统的功能。</p><h2>系统特性：</h2><ul><li>无需注册</li><li>支持SMTP/IMAP/POP3</li><li>REST API接口</li><li>附件支持</li></ul>',
 'From: <EMAIL>\r\nTo: <EMAIL>\r\nSubject: 欢迎使用临时邮箱系统\r\nContent-Type: text/plain; charset=utf-8\r\n\r\n这是一封测试邮件，用于演示临时邮箱系统的功能。',
 256),
('test-002@temp-email', '<EMAIL>', '<EMAIL>', '系统通知', 
 '您的临时邮箱已经准备就绪！\n\n您可以通过以下方式访问邮件：\n1. REST API\n2. IMAP客户端\n3. POP3客户端', 
 '<h2>系统通知</h2><p>您的临时邮箱已经准备就绪！</p><p>您可以通过以下方式访问邮件：</p><ol><li>REST API</li><li>IMAP客户端</li><li>POP3客户端</li></ol>',
 'From: <EMAIL>\r\nTo: <EMAIL>\r\nSubject: 系统通知\r\nContent-Type: text/html; charset=utf-8\r\n\r\n<h2>系统通知</h2><p>您的临时邮箱已经准备就绪！</p>',
 189);

-- 创建索引以提高查询性能
CREATE INDEX idx_emails_to_received ON emails(to_address, received_at DESC);
CREATE INDEX idx_emails_subject ON emails(subject(100));

-- 显示表结构
DESCRIBE emails;
DESCRIBE attachments;
