#!/bin/bash

# 临时邮箱系统发布脚本
# 支持完整的构建、测试、打包流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
APP_NAME="temp-email-system"
VERSION=${VERSION:-"1.0.0"}
BUILD_TIME=$(date +%Y-%m-%d_%H:%M:%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 目录定义
BUILD_DIR="build"
DIST_DIR="dist"
RELEASE_DIR="release"

# 支持的平台
PLATFORMS=(
    "windows/amd64"
    "linux/amd64"
    "linux/arm64"
    "linux/arm"
    "darwin/amd64"
    "darwin/arm64"
)

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    临时邮箱系统发布脚本                      ║"
    echo "║                Temporary Email System Release                ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║ 版本: $VERSION"
    echo "║ 构建时间: $BUILD_TIME"
    echo "║ Git提交: $GIT_COMMIT"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查依赖
check_dependencies() {
    print_info "检查构建依赖..."
    
    if ! command -v go &> /dev/null; then
        print_error "Go语言环境未安装"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_warning "Git未安装，将使用默认提交信息"
    fi
    
    print_success "依赖检查完成"
}

# 清理构建文件
clean_build() {
    print_info "清理构建文件..."
    rm -rf $BUILD_DIR
    rm -rf $DIST_DIR
    rm -rf $RELEASE_DIR
    rm -f ${APP_NAME}-*.zip
    rm -f ${APP_NAME}-*.tar.gz
    print_success "清理完成"
}

# 安装Go依赖
install_deps() {
    print_info "安装Go依赖..."
    go mod tidy
    go mod download
    print_success "依赖安装完成"
}

# 运行测试
run_tests() {
    print_info "运行测试..."
    if go test -v ./...; then
        print_success "所有测试通过"
    else
        print_error "测试失败"
        exit 1
    fi
}

# 代码检查
run_lint() {
    print_info "运行代码检查..."
    if command -v golangci-lint &> /dev/null; then
        golangci-lint run
        print_success "代码检查通过"
    else
        print_warning "golangci-lint未安装，跳过代码检查"
    fi
}

# 编译单个平台
build_platform() {
    local platform=$1
    local os=$(echo $platform | cut -d'/' -f1)
    local arch=$(echo $platform | cut -d'/' -f2)
    
    print_info "编译 $os/$arch..."
    
    local output_dir="$BUILD_DIR/$os-$arch"
    mkdir -p $output_dir
    
    local binary_name=$APP_NAME
    if [ "$os" = "windows" ]; then
        binary_name="${APP_NAME}.exe"
    fi
    
    local ldflags="-s -w -X main.Version=$VERSION -X main.BuildTime=$BUILD_TIME -X main.GitCommit=$GIT_COMMIT"
    
    if [ "$arch" = "arm" ]; then
        GOOS=$os GOARCH=$arch GOARM=7 go build -ldflags="$ldflags" -o "$output_dir/$binary_name" .
    else
        GOOS=$os GOARCH=$arch go build -ldflags="$ldflags" -o "$output_dir/$binary_name" .
    fi
    
    if [ $? -eq 0 ]; then
        print_success "$os/$arch 编译完成"
    else
        print_error "$os/$arch 编译失败"
        exit 1
    fi
}

# 编译所有平台
build_all() {
    print_info "开始编译所有平台..."
    
    for platform in "${PLATFORMS[@]}"; do
        build_platform $platform
    done
    
    print_success "所有平台编译完成"
}

# 复制配置文件
copy_files() {
    print_info "复制配置文件和文档..."
    
    for dir in $BUILD_DIR/*/; do
        if [ -d "$dir" ]; then
            # 复制配置文件
            cp -r conf "$dir"
            cp README.md "$dir"
            cp REDIS_CACHE_GUIDE.md "$dir"
            cp docker-compose.yml "$dir"
            cp init.sql "$dir"
            
            # 根据平台复制启动脚本
            if [[ "$dir" == *"windows"* ]]; then
                cp start.bat "$dir"
            else
                cp start.sh "$dir"
                chmod +x "$dir/start.sh"
                chmod +x "$dir/$APP_NAME"
            fi
            
            # 创建版本文件
            cat > "$dir/VERSION" << EOF
应用名称: $APP_NAME
版本: $VERSION
构建时间: $BUILD_TIME
Git提交: $GIT_COMMIT
平台: $(basename "$dir")
EOF
        fi
    done
    
    print_success "文件复制完成"
}

# 创建发布包
create_packages() {
    print_info "创建发布包..."
    
    mkdir -p $DIST_DIR
    
    for dir in $BUILD_DIR/*/; do
        if [ -d "$dir" ]; then
            local platform=$(basename "$dir")
            local package_name="${APP_NAME}-${VERSION}-${platform}"
            
            if [[ "$platform" == *"windows"* ]]; then
                # Windows使用zip格式
                cd "$dir"
                zip -r "../../$DIST_DIR/${package_name}.zip" .
                cd - > /dev/null
                print_success "创建Windows包: ${package_name}.zip"
            else
                # 其他平台使用tar.gz格式
                cd "$dir"
                tar -czf "../../$DIST_DIR/${package_name}.tar.gz" .
                cd - > /dev/null
                print_success "创建包: ${package_name}.tar.gz"
            fi
        fi
    done
}

# 创建发布说明
create_release_notes() {
    print_info "创建发布说明..."
    
    mkdir -p $RELEASE_DIR
    
    cat > "$RELEASE_DIR/RELEASE_NOTES.md" << EOF
# 临时邮箱系统 v$VERSION 发布说明

## 版本信息
- **版本**: $VERSION
- **构建时间**: $BUILD_TIME
- **Git提交**: $GIT_COMMIT

## 支持平台
- Windows AMD64
- Linux AMD64
- Linux ARM64
- Linux ARM (ARMv7)
- macOS AMD64 (Intel)
- macOS ARM64 (Apple Silicon)

## 功能特性
- ✅ 无需注册的临时邮箱服务
- ✅ 支持SMTP/IMAP/POP3协议
- ✅ RESTful API接口
- ✅ Redis缓存优化
- ✅ 邮件附件支持
- ✅ 跨平台支持

## 安装说明

### Windows
1. 下载 \`${APP_NAME}-${VERSION}-windows-amd64.zip\`
2. 解压到任意目录
3. 运行 \`start.bat\` 启动服务

### Linux
1. 下载对应架构的tar.gz文件
2. 解压: \`tar -xzf ${APP_NAME}-${VERSION}-linux-amd64.tar.gz\`
3. 运行: \`./start.sh\`

### macOS
1. 下载对应架构的tar.gz文件
2. 解压: \`tar -xzf ${APP_NAME}-${VERSION}-darwin-amd64.tar.gz\`
3. 运行: \`./start.sh\`

## 配置要求
- MySQL 5.7+ (可选，支持Docker)
- Redis 6.0+ (可选，支持Docker)

## 快速开始
使用Docker Compose一键启动：
\`\`\`bash
docker-compose up -d
\`\`\`

## 更多信息
请查看项目文档：
- README.md - 基础使用说明
- REDIS_CACHE_GUIDE.md - Redis缓存配置指南
EOF

    print_success "发布说明创建完成"
}

# 显示构建结果
show_results() {
    print_info "构建结果统计:"
    echo ""
    
    # 显示二进制文件大小
    for dir in $BUILD_DIR/*/; do
        if [ -d "$dir" ]; then
            local platform=$(basename "$dir")
            local binary="$dir/$APP_NAME"
            if [[ "$platform" == *"windows"* ]]; then
                binary="$dir/${APP_NAME}.exe"
            fi
            
            if [ -f "$binary" ]; then
                local size=$(stat -c%s "$binary" 2>/dev/null || stat -f%z "$binary" 2>/dev/null)
                local size_mb=$(echo "scale=2; $size/1024/1024" | bc 2>/dev/null || echo "N/A")
                printf "  %-20s: %10s 字节 (%s MB)\n" "$platform" "$size" "$size_mb"
            fi
        fi
    done
    
    echo ""
    
    # 显示发布包
    if [ -d "$DIST_DIR" ]; then
        print_info "发布包:"
        ls -la $DIST_DIR/
    fi
}

# 主函数
main() {
    print_header
    
    # 解析命令行参数
    case "${1:-all}" in
        "clean")
            clean_build
            ;;
        "deps")
            install_deps
            ;;
        "test")
            run_tests
            ;;
        "lint")
            run_lint
            ;;
        "build")
            check_dependencies
            install_deps
            build_all
            copy_files
            ;;
        "package")
            create_packages
            ;;
        "all")
            check_dependencies
            clean_build
            install_deps
            run_tests
            run_lint
            build_all
            copy_files
            create_packages
            create_release_notes
            show_results
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  all      - 完整发布流程（默认）"
            echo "  clean    - 清理构建文件"
            echo "  deps     - 安装依赖"
            echo "  test     - 运行测试"
            echo "  lint     - 代码检查"
            echo "  build    - 编译所有平台"
            echo "  package  - 创建发布包"
            echo "  help     - 显示帮助"
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
    
    print_success "操作完成！"
}

# 运行主函数
main "$@"
