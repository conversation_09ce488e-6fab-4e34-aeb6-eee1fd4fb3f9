#!/bin/bash

echo "=== 临时邮箱系统性能测试 ==="

# 检查依赖
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装"
    exit 1
fi

if ! command -v curl &> /dev/null; then
    echo "❌ curl未安装"
    exit 1
fi

# 配置
BASE_URL="http://localhost:8080"
TEST_EMAIL="<EMAIL>"
CONCURRENT_USERS=10
REQUESTS_PER_USER=100

echo "📊 性能测试配置:"
echo "  - 基础URL: $BASE_URL"
echo "  - 测试邮箱: $TEST_EMAIL"
echo "  - 并发用户: $CONCURRENT_USERS"
echo "  - 每用户请求数: $REQUESTS_PER_USER"
echo ""

# 检查服务是否运行
echo "🔍 检查服务状态..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ 服务未运行，请先启动服务"
    exit 1
fi
echo "✅ 服务正常运行"

# 创建测试目录
mkdir -p benchmark_results
cd benchmark_results

# 1. Go基准测试
echo ""
echo "🧪 运行Go基准测试..."
cd ..
go test -bench=. -benchmem -cpuprofile=benchmark_results/cpu.prof -memprofile=benchmark_results/mem.prof ./... > benchmark_results/go_benchmark.txt 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Go基准测试完成"
else
    echo "⚠️  Go基准测试失败，请检查代码"
fi

# 2. API性能测试
echo ""
echo "🚀 运行API性能测试..."

# 健康检查API测试
echo "测试健康检查API..."
curl -w "@-" -s -o /dev/null "$BASE_URL/health" <<'EOF' > benchmark_results/health_api.txt
     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n
EOF

# 邮件列表API测试
echo "测试邮件列表API..."
for i in {1..10}; do
    curl -w "%{time_total}\n" -s -o /dev/null "$BASE_URL/api/v1/emails/$TEST_EMAIL" >> benchmark_results/email_list_times.txt
done

# 3. 并发测试
echo ""
echo "⚡ 运行并发测试..."

# 创建并发测试脚本
cat > benchmark_results/concurrent_test.sh << 'EOF'
#!/bin/bash
URL=$1
REQUESTS=$2
USER_ID=$3

echo "用户 $USER_ID 开始测试..."
start_time=$(date +%s.%N)

for ((i=1; i<=REQUESTS; i++)); do
    curl -s -o /dev/null "$URL"
    if [ $((i % 10)) -eq 0 ]; then
        echo "用户 $USER_ID: 完成 $i/$REQUESTS 请求"
    fi
done

end_time=$(date +%s.%N)
duration=$(echo "$end_time - $start_time" | bc)
rps=$(echo "scale=2; $REQUESTS / $duration" | bc)

echo "用户 $USER_ID 完成: $REQUESTS 请求, 耗时: ${duration}s, RPS: $rps"
echo "$USER_ID,$REQUESTS,$duration,$rps" >> concurrent_results.csv
EOF

chmod +x benchmark_results/concurrent_test.sh

# 初始化结果文件
echo "user_id,requests,duration,rps" > benchmark_results/concurrent_results.csv

# 启动并发测试
echo "启动 $CONCURRENT_USERS 个并发用户..."
for ((i=1; i<=CONCURRENT_USERS; i++)); do
    (cd benchmark_results && ./concurrent_test.sh "$BASE_URL/health" $REQUESTS_PER_USER $i) &
done

# 等待所有测试完成
wait

# 4. 内存和CPU分析
echo ""
echo "📈 生成性能分析报告..."

if [ -f "benchmark_results/cpu.prof" ]; then
    echo "生成CPU分析报告..."
    go tool pprof -text benchmark_results/cpu.prof > benchmark_results/cpu_analysis.txt 2>/dev/null || echo "CPU分析失败"
fi

if [ -f "benchmark_results/mem.prof" ]; then
    echo "生成内存分析报告..."
    go tool pprof -text benchmark_results/mem.prof > benchmark_results/mem_analysis.txt 2>/dev/null || echo "内存分析失败"
fi

# 5. 生成汇总报告
echo ""
echo "📋 生成测试报告..."

cat > benchmark_results/summary.md << EOF
# 性能测试报告

## 测试配置
- 测试时间: $(date)
- 基础URL: $BASE_URL
- 并发用户数: $CONCURRENT_USERS
- 每用户请求数: $REQUESTS_PER_USER

## API响应时间

### 健康检查API
\`\`\`
$(cat benchmark_results/health_api.txt)
\`\`\`

### 邮件列表API (10次请求的响应时间)
\`\`\`
$(cat benchmark_results/email_list_times.txt | awk '{sum+=$1; count++} END {printf "平均响应时间: %.3fs\n最小响应时间: %.3fs\n最大响应时间: %.3fs\n", sum/count, min, max}' min=999 max=0)
\`\`\`

## 并发测试结果
\`\`\`
$(awk -F',' 'NR>1 {sum+=$4; count++} END {printf "平均RPS: %.2f\n总请求数: %d\n", sum/count, count*$REQUESTS_PER_USER}' benchmark_results/concurrent_results.csv)
\`\`\`

## Go基准测试
\`\`\`
$(head -20 benchmark_results/go_benchmark.txt)
\`\`\`

EOF

echo "✅ 性能测试完成！"
echo ""
echo "📁 测试结果保存在 benchmark_results/ 目录中:"
echo "  - summary.md           - 测试汇总报告"
echo "  - go_benchmark.txt     - Go基准测试结果"
echo "  - concurrent_results.csv - 并发测试数据"
echo "  - cpu.prof / mem.prof  - 性能分析文件"
echo ""
echo "📖 查看汇总报告:"
echo "  cat benchmark_results/summary.md"
