<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 临时邮箱系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📧</text></svg>">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="/static/css/api.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h1>📧 临时邮箱系统</h1>
            </div>
            <div class="nav-menu">
                <a href="/" class="nav-link">首页</a>
                <a href="/mailbox" class="nav-link">邮箱管理</a>
                <a href="/api" class="nav-link active">API文档</a>
                <a href="/config" class="nav-link">配置说明</a>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="main-content">
        <div class="container">
            <div class="api-layout">
                <!-- 侧边栏导航 -->
                <aside class="api-sidebar">
                    <h3>API接口</h3>
                    <nav class="api-nav">
                        <div class="nav-group">
                            <h4>邮件管理</h4>
                            <a href="#get-emails" class="nav-item">获取邮件列表</a>
                            <a href="#get-email-detail" class="nav-item">获取邮件详情</a>
                            <a href="#send-email" class="nav-item">发送邮件</a>
                            <a href="#get-attachment" class="nav-item">下载附件</a>
                        </div>
                        <div class="nav-group">
                            <h4>缓存管理</h4>
                            <a href="#cache-stats" class="nav-item">缓存统计</a>
                            <a href="#clear-cache" class="nav-item">清除缓存</a>
                            <a href="#cache-warmup" class="nav-item">缓存预热</a>
                        </div>
                        <div class="nav-group">
                            <h4>系统监控</h4>
                            <a href="#health-check" class="nav-item">健康检查</a>
                            <a href="#system-metrics" class="nav-item">系统指标</a>
                        </div>
                    </nav>
                </aside>

                <!-- API文档内容 -->
                <div class="api-content">
                    <div class="api-header">
                        <h2>REST API 文档</h2>
                        <p>临时邮箱系统提供完整的REST API接口，支持邮件管理、缓存控制和系统监控。</p>
                        <div class="api-base-url">
                            <label>Base URL:</label>
                            <code id="baseUrl">http://localhost:8080/api/v1</code>
                        </div>
                    </div>

                    <!-- 邮件管理API -->
                    <section class="api-section" id="get-emails">
                        <h3>获取邮件列表</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/emails/{email_address}</code>
                        </div>
                        <div class="api-description">
                            <p>获取指定邮箱地址的邮件列表，支持分页和内容过滤。</p>
                        </div>
                        <div class="api-params">
                            <h4>路径参数</h4>
                            <table class="params-table">
                                <tr>
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>必需</th>
                                    <th>说明</th>
                                </tr>
                                <tr>
                                    <td>email_address</td>
                                    <td>string</td>
                                    <td>是</td>
                                    <td>邮箱地址</td>
                                </tr>
                            </table>
                            <h4>查询参数</h4>
                            <table class="params-table">
                                <tr>
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>默认值</th>
                                    <th>说明</th>
                                </tr>
                                <tr>
                                    <td>page</td>
                                    <td>int</td>
                                    <td>1</td>
                                    <td>页码</td>
                                </tr>
                                <tr>
                                    <td>limit</td>
                                    <td>int</td>
                                    <td>20</td>
                                    <td>每页数量</td>
                                </tr>
                                <tr>
                                    <td>include_content</td>
                                    <td>bool</td>
                                    <td>false</td>
                                    <td>是否包含邮件内容</td>
                                </tr>
                            </table>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <div class="form-group">
                                    <label>邮箱地址:</label>
                                    <input type="text" id="emailAddress1" placeholder="<EMAIL>" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>页码:</label>
                                    <input type="number" id="page1" value="1" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>每页数量:</label>
                                    <input type="number" id="limit1" value="20" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="includeContent1"> 包含邮件内容
                                    </label>
                                </div>
                                <button class="btn btn-primary" onclick="testGetEmails()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result1" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 更多API接口... -->
                    <section class="api-section" id="get-email-detail">
                        <h3>获取邮件详情</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/emails/{email_address}/{email_id}</code>
                        </div>
                        <div class="api-description">
                            <p>获取指定邮件的详细信息，包括完整内容和附件。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <div class="form-group">
                                    <label>邮箱地址:</label>
                                    <input type="text" id="emailAddress2" placeholder="<EMAIL>" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>邮件ID:</label>
                                    <input type="number" id="emailId2" placeholder="1" class="form-input">
                                </div>
                                <button class="btn btn-primary" onclick="testGetEmailDetail()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result2" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 健康检查API -->
                    <section class="api-section" id="health-check">
                        <h3>健康检查</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/health</code>
                        </div>
                        <div class="api-description">
                            <p>检查系统健康状态，包括数据库连接、Redis连接等。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <button class="btn btn-primary" onclick="testHealthCheck()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result3" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 发送邮件API -->
                    <section class="api-section" id="send-email">
                        <h3>发送邮件</h3>
                        <div class="api-endpoint">
                            <span class="method post">POST</span>
                            <code>/emails/send</code>
                        </div>
                        <div class="api-description">
                            <p>发送邮件到指定地址。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <div class="form-group">
                                    <label>发件人:</label>
                                    <input type="text" id="sendFrom" placeholder="<EMAIL>" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>收件人:</label>
                                    <input type="text" id="sendTo" placeholder="<EMAIL>" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>主题:</label>
                                    <input type="text" id="sendSubject" placeholder="邮件主题" class="form-input">
                                </div>
                                <div class="form-group">
                                    <label>内容:</label>
                                    <textarea id="sendContent" placeholder="邮件内容" class="form-input" rows="4"></textarea>
                                </div>
                                <button class="btn btn-primary" onclick="testSendEmail()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result5" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 下载附件API -->
                    <section class="api-section" id="get-attachment">
                        <h3>下载附件</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/emails/{email_address}/attachments/{attachment_id}</code>
                        </div>
                        <div class="api-description">
                            <p>下载指定邮件的附件文件。</p>
                        </div>
                        <div class="api-params">
                            <h4>路径参数</h4>
                            <table class="params-table">
                                <tr>
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>必需</th>
                                    <th>说明</th>
                                </tr>
                                <tr>
                                    <td>email_address</td>
                                    <td>string</td>
                                    <td>是</td>
                                    <td>邮箱地址</td>
                                </tr>
                                <tr>
                                    <td>attachment_id</td>
                                    <td>int</td>
                                    <td>是</td>
                                    <td>附件ID</td>
                                </tr>
                            </table>
                        </div>
                    </section>

                    <!-- 缓存统计API -->
                    <section class="api-section" id="cache-stats">
                        <h3>缓存统计</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/cache/stats</code>
                        </div>
                        <div class="api-description">
                            <p>获取缓存系统的统计信息，包括命中率、错误次数等。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <button class="btn btn-primary" onclick="testCacheStats()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result4" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 清除缓存API -->
                    <section class="api-section" id="clear-cache">
                        <h3>清除缓存</h3>
                        <div class="api-endpoint">
                            <span class="method post">POST</span>
                            <code>/cache/clear</code>
                        </div>
                        <div class="api-description">
                            <p>清除指定模式的缓存数据。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <div class="form-group">
                                    <label>缓存模式 (可选):</label>
                                    <input type="text" id="cachePattern" placeholder="email:list:*" class="form-input">
                                </div>
                                <button class="btn btn-primary" onclick="testClearCache()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result6" class="result-code"></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 系统指标API -->
                    <section class="api-section" id="system-metrics">
                        <h3>系统指标</h3>
                        <div class="api-endpoint">
                            <span class="method get">GET</span>
                            <code>/health/metrics</code>
                        </div>
                        <div class="api-description">
                            <p>获取详细的系统性能指标和运行状态。</p>
                        </div>
                        <div class="api-test">
                            <h4>在线测试</h4>
                            <div class="test-form">
                                <button class="btn btn-primary" onclick="testSystemMetrics()">发送请求</button>
                            </div>
                            <div class="test-result">
                                <h5>响应结果:</h5>
                                <pre id="result7" class="result-code"></pre>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </main>

    <script src="/static/js/common.js"></script>
    <script src="/static/js/api.js"></script>
</body>
</html>
