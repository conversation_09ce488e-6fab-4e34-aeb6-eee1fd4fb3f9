#!/bin/bash

# 临时邮箱系统安装脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目信息
APP_NAME="temp-email-system"
INSTALL_DIR="/opt/temp-email-system"
SERVICE_NAME="temp-email-system"
USER_NAME="temp-email"

# 打印消息函数
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    临时邮箱系统安装脚本                      ║"
    echo "║                Temporary Email System Installer              ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root权限运行此脚本"
        echo "使用: sudo $0"
        exit 1
    fi
}

# 检测系统类型
detect_system() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    print_info "检测到系统: $OS $VER"
}

# 检测架构
detect_arch() {
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            ARCH="amd64"
            ;;
        aarch64)
            ARCH="arm64"
            ;;
        armv7l)
            ARCH="arm"
            ;;
        *)
            print_error "不支持的架构: $ARCH"
            exit 1
            ;;
    esac
    
    print_info "检测到架构: $ARCH"
}

# 创建用户
create_user() {
    if ! id "$USER_NAME" &>/dev/null; then
        print_info "创建用户: $USER_NAME"
        useradd --system --shell /bin/false --home-dir $INSTALL_DIR --create-home $USER_NAME
        print_success "用户创建完成"
    else
        print_info "用户 $USER_NAME 已存在"
    fi
}

# 创建目录
create_directories() {
    print_info "创建安装目录..."
    
    mkdir -p $INSTALL_DIR
    mkdir -p $INSTALL_DIR/conf
    mkdir -p $INSTALL_DIR/logs
    mkdir -p /etc/temp-email-system
    mkdir -p /var/log/temp-email-system
    
    # 设置权限
    chown -R $USER_NAME:$USER_NAME $INSTALL_DIR
    chown -R $USER_NAME:$USER_NAME /var/log/temp-email-system
    
    print_success "目录创建完成"
}

# 下载或复制文件
install_files() {
    print_info "安装应用文件..."
    
    # 如果当前目录有编译好的文件，直接复制
    if [ -f "./$APP_NAME" ]; then
        cp ./$APP_NAME $INSTALL_DIR/
        chmod +x $INSTALL_DIR/$APP_NAME
        print_success "应用程序复制完成"
    else
        print_error "未找到应用程序文件: $APP_NAME"
        print_info "请先编译应用程序或下载发布包"
        exit 1
    fi
    
    # 复制配置文件
    if [ -d "./conf" ]; then
        cp -r ./conf/* $INSTALL_DIR/conf/
        print_success "配置文件复制完成"
    fi
    
    # 复制文档
    [ -f "./README.md" ] && cp ./README.md $INSTALL_DIR/
    [ -f "./REDIS_CACHE_GUIDE.md" ] && cp ./REDIS_CACHE_GUIDE.md $INSTALL_DIR/
    
    # 设置权限
    chown -R $USER_NAME:$USER_NAME $INSTALL_DIR
}

# 创建systemd服务
create_service() {
    print_info "创建systemd服务..."
    
    cat > /etc/systemd/system/$SERVICE_NAME.service << EOF
[Unit]
Description=Temporary Email System
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=simple
User=$USER_NAME
Group=$USER_NAME
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/$APP_NAME
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 环境变量
Environment=GIN_MODE=release

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR /var/log/temp-email-system

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    print_success "systemd服务创建完成"
}

# 安装依赖
install_dependencies() {
    print_info "安装系统依赖..."
    
    # 检测包管理器并安装依赖
    if command -v apt-get >/dev/null 2>&1; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y curl wget
    elif command -v yum >/dev/null 2>&1; then
        # CentOS/RHEL
        yum install -y curl wget
    elif command -v dnf >/dev/null 2>&1; then
        # Fedora
        dnf install -y curl wget
    else
        print_warning "未知的包管理器，请手动安装curl和wget"
    fi
    
    print_success "系统依赖安装完成"
}

# 配置防火墙
configure_firewall() {
    print_info "配置防火墙..."
    
    # 检查并配置防火墙
    if command -v ufw >/dev/null 2>&1; then
        # Ubuntu防火墙
        ufw allow 8080/tcp comment "Temp Email Web API"
        ufw allow 2525/tcp comment "Temp Email SMTP"
        ufw allow 1143/tcp comment "Temp Email IMAP"
        ufw allow 1110/tcp comment "Temp Email POP3"
        print_success "UFW防火墙配置完成"
    elif command -v firewall-cmd >/dev/null 2>&1; then
        # CentOS/RHEL防火墙
        firewall-cmd --permanent --add-port=8080/tcp
        firewall-cmd --permanent --add-port=2525/tcp
        firewall-cmd --permanent --add-port=1143/tcp
        firewall-cmd --permanent --add-port=1110/tcp
        firewall-cmd --reload
        print_success "firewalld防火墙配置完成"
    else
        print_warning "未检测到防火墙，请手动开放端口: 8080, 2525, 1143, 1110"
    fi
}

# 启动服务
start_service() {
    print_info "启动服务..."
    
    systemctl enable $SERVICE_NAME
    systemctl start $SERVICE_NAME
    
    # 等待服务启动
    sleep 3
    
    if systemctl is-active --quiet $SERVICE_NAME; then
        print_success "服务启动成功"
    else
        print_error "服务启动失败"
        print_info "查看日志: journalctl -u $SERVICE_NAME -f"
        exit 1
    fi
}

# 显示安装结果
show_result() {
    print_success "安装完成！"
    echo ""
    echo "服务信息:"
    echo "  服务名称: $SERVICE_NAME"
    echo "  安装目录: $INSTALL_DIR"
    echo "  配置目录: $INSTALL_DIR/conf"
    echo "  日志目录: /var/log/temp-email-system"
    echo ""
    echo "服务端口:"
    echo "  Web API: http://localhost:8080"
    echo "  SMTP:    localhost:2525"
    echo "  IMAP:    localhost:1143"
    echo "  POP3:    localhost:1110"
    echo ""
    echo "常用命令:"
    echo "  启动服务: systemctl start $SERVICE_NAME"
    echo "  停止服务: systemctl stop $SERVICE_NAME"
    echo "  重启服务: systemctl restart $SERVICE_NAME"
    echo "  查看状态: systemctl status $SERVICE_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "配置文件: $INSTALL_DIR/conf/app.conf"
    echo ""
}

# 卸载函数
uninstall() {
    print_info "卸载临时邮箱系统..."
    
    # 停止并禁用服务
    systemctl stop $SERVICE_NAME 2>/dev/null || true
    systemctl disable $SERVICE_NAME 2>/dev/null || true
    
    # 删除服务文件
    rm -f /etc/systemd/system/$SERVICE_NAME.service
    systemctl daemon-reload
    
    # 删除安装目录
    rm -rf $INSTALL_DIR
    rm -rf /var/log/temp-email-system
    rm -rf /etc/temp-email-system
    
    # 删除用户
    userdel $USER_NAME 2>/dev/null || true
    
    print_success "卸载完成"
}

# 主函数
main() {
    print_header
    
    case "${1:-install}" in
        "install")
            check_root
            detect_system
            detect_arch
            install_dependencies
            create_user
            create_directories
            install_files
            create_service
            configure_firewall
            start_service
            show_result
            ;;
        "uninstall")
            check_root
            uninstall
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  install   - 安装临时邮箱系统（默认）"
            echo "  uninstall - 卸载临时邮箱系统"
            echo "  help      - 显示帮助"
            ;;
        *)
            print_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
