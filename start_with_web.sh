#!/bin/bash

# 临时邮箱系统启动脚本（包含Web界面）

echo "🚀 启动临时邮箱系统..."
echo "================================"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装，请先安装Go语言环境"
    exit 1
fi

echo "✅ Go环境检查通过"

# 检查MySQL服务
if ! command -v mysql &> /dev/null; then
    echo "⚠️  MySQL客户端未找到，请确保MySQL服务正在运行"
else
    echo "✅ MySQL环境检查通过"
fi

# 检查Redis服务（可选）
if ! command -v redis-cli &> /dev/null; then
    echo "⚠️  Redis客户端未找到，系统将使用数据库模式"
else
    echo "✅ Redis环境检查通过"
fi

# 设置Go代理（解决网络问题）
echo "🔧 配置Go代理..."
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn

# 下载依赖
echo "📦 下载Go模块依赖..."
go mod tidy

if [ $? -ne 0 ]; then
    echo "❌ 依赖下载失败，请检查网络连接"
    exit 1
fi

echo "✅ 依赖下载完成"

# 编译项目
echo "🔨 编译项目..."
go build -o temp-email-system .

if [ $? -ne 0 ]; then
    echo "❌ 编译失败，请检查代码"
    exit 1
fi

echo "✅ 编译完成"

# 启动系统
echo "🌟 启动临时邮箱系统..."
echo "================================"
echo "📧 Web界面: http://localhost:8080"
echo "📧 邮箱管理: http://localhost:8080/mailbox"
echo "📧 API文档: http://localhost:8080/api"
echo "📧 配置说明: http://localhost:8080/config"
echo "================================"
echo "🔧 SMTP端口: 2525"
echo "🔧 IMAP端口: 1143"
echo "🔧 POP3端口: 1110"
echo "================================"
echo "按 Ctrl+C 停止服务"
echo ""

# 运行系统
./temp-email-system
