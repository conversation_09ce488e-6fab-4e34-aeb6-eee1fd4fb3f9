/* 配置页面样式 */

.config-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
    margin-top: 40px;
}

/* 配置侧边栏 */
.config-sidebar {
    background-color: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e5e7eb;
    height: fit-content;
    position: sticky;
    top: 100px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-sidebar h3 {
    font-size: 18px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 20px;
}

.config-nav {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.config-nav .nav-item {
    display: block;
    padding: 12px 16px;
    color: #64748b;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.config-nav .nav-item:hover {
    background-color: #f1f5f9;
    color: #2563eb;
}

.config-nav .nav-item.active {
    background-color: #dbeafe;
    color: #2563eb;
    font-weight: 600;
}

/* 配置内容区域 */
.config-content {
    background-color: white;
    border-radius: 12px;
    padding: 32px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-section {
    display: none;
}

.config-section.active {
    display: block;
}

.config-section h2 {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 32px;
    padding-bottom: 16px;
    border-bottom: 2px solid #e5e7eb;
}

/* 配置卡片 */
.config-card {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    transition: all 0.2s ease;
}

.config-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.config-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.config-card p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 16px;
}

.config-card ul {
    margin-left: 20px;
    margin-bottom: 16px;
}

.config-card li {
    color: #64748b;
    margin-bottom: 8px;
    line-height: 1.6;
}

.config-card li strong {
    color: #374151;
}

/* 配置表格 */
.config-table {
    margin: 16px 0;
}

.config-table table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

.config-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 14px;
}

.config-table td:first-child {
    font-weight: 600;
    color: #374151;
    background-color: #f8fafc;
    width: 120px;
}

.config-table td:last-child {
    color: #64748b;
}

.config-table tr:last-child td {
    border-bottom: none;
}

.config-table code {
    background-color: #f1f5f9;
    color: #059669;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    font-weight: 600;
}

/* 示例框 */
.example-box {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-top: 16px;
}

.example-box h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
}

.example-config p {
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.example-config ul {
    margin-left: 20px;
    margin-bottom: 20px;
}

.example-config li {
    margin-bottom: 6px;
    color: #64748b;
}

.success-list {
    list-style: none !important;
    margin-left: 0 !important;
}

.success-list li {
    color: #059669 !important;
    font-weight: 500;
    margin-bottom: 8px;
}

.warning-list {
    list-style: none !important;
    margin-left: 0 !important;
}

.warning-list li {
    color: #d97706 !important;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.warning-list li::before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0;
}

/* 域名和端口占位符样式 */
.domain-placeholder {
    color: #2563eb;
    font-weight: 600;
}

.server-placeholder {
    color: #059669;
    font-weight: 600;
}

.port-imap,
.port-pop3,
.port-smtp,
.port-http {
    color: #dc2626;
    font-weight: 700;
}

/* FAQ样式 */
.faq-item {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
}

.faq-item h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 8px;
}

.faq-item p {
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .config-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .config-sidebar {
        position: static;
        order: 2;
    }
    
    .config-content {
        order: 1;
    }
    
    .config-nav {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 8px;
    }
}

@media (max-width: 768px) {
    .config-content {
        padding: 20px;
    }
    
    .config-section h2 {
        font-size: 24px;
    }
    
    .config-card {
        padding: 16px;
    }
    
    .config-card h3 {
        font-size: 18px;
    }
    
    .config-table table {
        font-size: 12px;
    }
    
    .config-table td {
        padding: 8px 12px;
    }
    
    .config-table td:first-child {
        width: 100px;
    }
    
    .example-box {
        padding: 16px;
    }
    
    .faq-item {
        padding: 16px;
    }
}
