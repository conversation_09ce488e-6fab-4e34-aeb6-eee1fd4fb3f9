package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/beego/beego/v2/server/web"
)

// RedisService Redis缓存服务
type RedisService struct {
	client  *redis.Client
	enabled bool
	ctx     context.Context
}

// CacheStats 缓存统计信息
type CacheStats struct {
	HitCount   int64 `json:"hit_count"`
	MissCount  int64 `json:"miss_count"`
	ErrorCount int64 `json:"error_count"`
	HitRate    float64 `json:"hit_rate"`
}

var (
	redisService *RedisService
	cacheStats   = &CacheStats{}
	initOnce     sync.Once
	statsMutex   sync.RWMutex
)

// InitRedisService 初始化Redis服务（线程安全）
func InitRedisService() *RedisService {
	initOnce.Do(func() {
		redisService = initRedisServiceInternal()
	})
	return redisService
}

// initRedisServiceInternal 内部初始化函数
func initRedisServiceInternal() *RedisService {

	// 检查是否启用Redis
	enabled, _ := web.AppConfig.Bool("redis_enabled")
	if !enabled {
		log.Println("Redis缓存已禁用")
		return &RedisService{enabled: false, ctx: context.Background()}
	}

	// 获取Redis配置
	host, _ := web.AppConfig.String("redis_host")
	port, _ := web.AppConfig.String("redis_port")
	password, _ := web.AppConfig.String("redis_password")
	db, _ := web.AppConfig.Int("redis_db")
	poolSize, _ := web.AppConfig.Int("redis_pool_size")
	minIdleConns, _ := web.AppConfig.Int("redis_min_idle_conns")

	// 设置默认值
	if host == "" {
		host = "localhost"
	}
	if port == "" {
		port = "6379"
	}
	if poolSize == 0 {
		poolSize = 10
	}
	if minIdleConns == 0 {
		minIdleConns = 5
	}

	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%s", host, port),
		Password:     password,
		DB:           db,
		PoolSize:     poolSize,
		MinIdleConns: minIdleConns,
	})

	// 测试连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		log.Printf("Redis连接失败: %v，缓存功能将被禁用", err)
		return &RedisService{enabled: false, ctx: ctx}
	}

	service := &RedisService{
		client:  rdb,
		enabled: true,
		ctx:     ctx,
	}

	log.Printf("Redis缓存服务初始化成功 - %s:%s", host, port)
	return service
}

// GetRedisService 获取Redis服务实例
func GetRedisService() *RedisService {
	if redisService == nil {
		return InitRedisService()
	}
	return redisService
}

// IsEnabled 检查缓存是否启用
func (r *RedisService) IsEnabled() bool {
	return r.enabled
}

// Set 设置缓存
func (r *RedisService) Set(key string, value interface{}, expiration time.Duration) error {
	if !r.enabled {
		return nil
	}

	jsonData, err := json.Marshal(value)
	if err != nil {
		cacheStats.ErrorCount++
		return fmt.Errorf("序列化缓存数据失败: %v", err)
	}

	err = r.client.Set(r.ctx, key, jsonData, expiration).Err()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("设置缓存失败 [%s]: %v", key, err)
		return err
	}

	log.Printf("缓存设置成功 [%s], TTL: %v", key, expiration)
	return nil
}

// Get 获取缓存
func (r *RedisService) Get(key string, dest interface{}) error {
	if !r.enabled {
		cacheStats.MissCount++
		return redis.Nil
	}

	val, err := r.client.Get(r.ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			updateStats(func() { cacheStats.MissCount++ })
			log.Printf("缓存未命中 [%s]", key)
		} else {
			updateStats(func() { cacheStats.ErrorCount++ })
			log.Printf("获取缓存失败 [%s]: %v", key, err)
		}
		return err
	}

	err = json.Unmarshal([]byte(val), dest)
	if err != nil {
		updateStats(func() { cacheStats.ErrorCount++ })
		log.Printf("反序列化缓存数据失败 [%s]: %v", key, err)
		return err
	}

	updateStats(func() { cacheStats.HitCount++ })
	log.Printf("缓存命中 [%s]", key)
	return nil
}

// Delete 删除缓存
func (r *RedisService) Delete(key string) error {
	if !r.enabled {
		return nil
	}

	err := r.client.Del(r.ctx, key).Err()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("删除缓存失败 [%s]: %v", key, err)
		return err
	}

	log.Printf("缓存删除成功 [%s]", key)
	return nil
}

// DeletePattern 根据模式删除缓存
func (r *RedisService) DeletePattern(pattern string) error {
	if !r.enabled {
		return nil
	}

	keys, err := r.client.Keys(r.ctx, pattern).Result()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("查找缓存键失败 [%s]: %v", pattern, err)
		return err
	}

	if len(keys) == 0 {
		log.Printf("未找到匹配的缓存键 [%s]", pattern)
		return nil
	}

	err = r.client.Del(r.ctx, keys...).Err()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("批量删除缓存失败 [%s]: %v", pattern, err)
		return err
	}

	log.Printf("批量删除缓存成功 [%s], 删除数量: %d", pattern, len(keys))
	return nil
}

// Exists 检查缓存是否存在
func (r *RedisService) Exists(key string) bool {
	if !r.enabled {
		return false
	}

	count, err := r.client.Exists(r.ctx, key).Result()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("检查缓存存在性失败 [%s]: %v", key, err)
		return false
	}

	return count > 0
}

// GetTTL 获取缓存过期时间
func (r *RedisService) GetTTL(key string) time.Duration {
	if !r.enabled {
		return 0
	}

	ttl, err := r.client.TTL(r.ctx, key).Result()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("获取缓存TTL失败 [%s]: %v", key, err)
		return 0
	}

	return ttl
}

// FlushAll 清空所有缓存
func (r *RedisService) FlushAll() error {
	if !r.enabled {
		return nil
	}

	err := r.client.FlushAll(r.ctx).Err()
	if err != nil {
		cacheStats.ErrorCount++
		log.Printf("清空所有缓存失败: %v", err)
		return err
	}

	log.Println("所有缓存已清空")
	return nil
}

// GetStats 获取缓存统计信息（线程安全）
func (r *RedisService) GetStats() *CacheStats {
	statsMutex.RLock()
	defer statsMutex.RUnlock()

	// 创建副本避免并发修改
	stats := &CacheStats{
		HitCount:   cacheStats.HitCount,
		MissCount:  cacheStats.MissCount,
		ErrorCount: cacheStats.ErrorCount,
	}

	total := stats.HitCount + stats.MissCount
	if total > 0 {
		stats.HitRate = float64(stats.HitCount) / float64(total) * 100
	}

	return stats
}

// ResetStats 重置缓存统计信息（线程安全）
func (r *RedisService) ResetStats() {
	statsMutex.Lock()
	defer statsMutex.Unlock()
	cacheStats = &CacheStats{}
}

// updateStats 线程安全地更新统计信息
func updateStats(fn func()) {
	statsMutex.Lock()
	defer statsMutex.Unlock()
	fn()
}

// Ping 检查Redis连接
func (r *RedisService) Ping() error {
	if !r.enabled {
		return fmt.Errorf("Redis服务未启用")
	}

	_, err := r.client.Ping(r.ctx).Result()
	return err
}

// Close 关闭Redis连接
func (r *RedisService) Close() error {
	if r.client != nil {
		return r.client.Close()
	}
	return nil
}

// GetCacheTTL 获取配置的缓存TTL
func GetCacheTTL(cacheType string) time.Duration {
	var ttlSeconds int
	var err error

	switch cacheType {
	case "email_list":
		ttlSeconds, err = web.AppConfig.Int("cache_email_list_ttl")
	case "email_detail":
		ttlSeconds, err = web.AppConfig.Int("cache_email_detail_ttl")
	case "email_stats":
		ttlSeconds, err = web.AppConfig.Int("cache_email_stats_ttl")
	default:
		ttlSeconds = 300 // 默认5分钟
	}

	if err != nil || ttlSeconds <= 0 {
		// 设置默认值
		switch cacheType {
		case "email_list":
			ttlSeconds = 300  // 5分钟
		case "email_detail":
			ttlSeconds = 1800 // 30分钟
		case "email_stats":
			ttlSeconds = 600  // 10分钟
		default:
			ttlSeconds = 300
		}
	}

	return time.Duration(ttlSeconds) * time.Second
}
