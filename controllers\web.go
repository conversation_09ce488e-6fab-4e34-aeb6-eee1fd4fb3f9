package controllers

import (
	"github.com/beego/beego/v2/server/web"
)

// WebController Web界面控制器
type WebController struct {
	web.Controller
}

// Index 主页面
func (c *WebController) Index() {
	c.TplName = "index.html"
}

// API API文档页面
func (c *WebController) API() {
	c.TplName = "api.html"
}

// Mailbox 邮箱管理页面
func (c *WebController) Mailbox() {
	c.TplName = "mailbox.html"
}

// Config 配置说明页面
func (c *WebController) Config() {
	c.TplName = "config.html"
}

// GetSystemInfo 获取系统信息API（用于前端显示）
func (c *WebController) GetSystemInfo() {
	// 获取系统配置信息
	mailDomain, _ := web.AppConfig.String("mail_domain")
	if mailDomain == "" {
		mailDomain = "yourdomain.com"
	}

	smtpPort, _ := web.AppConfig.String("smtp_port")
	if smtpPort == "" {
		smtpPort = "2525"
	}

	imapPort, _ := web.AppConfig.String("imap_port")
	if imapPort == "" {
		imapPort = "1143"
	}

	pop3Port, _ := web.AppConfig.String("pop3_port")
	if pop3Port == "" {
		pop3Port = "1110"
	}

	httpPort, _ := web.AppConfig.String("httpport")
	if httpPort == "" {
		httpPort = "8080"
	}

	info := map[string]interface{}{
		"mail_domain": mailDomain,
		"smtp_port":   smtpPort,
		"imap_port":   imapPort,
		"pop3_port":   pop3Port,
		"http_port":   httpPort,
		"version":     "1.0.0",
		"name":        "临时邮箱系统",
	}

	// 直接返回JSON响应
	c.Data["json"] = map[string]interface{}{
		"code":    200,
		"message": "success",
		"data":    info,
	}
	c.ServeJSON()
}
