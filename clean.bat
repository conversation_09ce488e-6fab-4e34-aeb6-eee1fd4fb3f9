@echo off
echo === Temporary Email System - Project Cleanup ===

echo Cleaning build artifacts...

REM Remove executable files
if exist "temp-email-system.exe" del "temp-email-system.exe"
if exist "temp-email-system" del "temp-email-system"

REM Remove build directories
if exist "build" rmdir /s /q "build" 2>nul
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "release" rmdir /s /q "release" 2>nul

REM Remove release packages
del /q *.zip 2>nul
del /q *.tar.gz 2>nul
del /q *.tar.bz2 2>nul

REM Remove log files
del /q *.log 2>nul
if exist "logs" rmdir /s /q "logs" 2>nul

REM Remove temporary files
del /q *.tmp 2>nul
del /q *.temp 2>nul
del /q *.bak 2>nul
del /q *.backup 2>nul

REM Remove test files
del /q *.test 2>nul
del /q coverage.txt 2>nul
del /q coverage.html 2>nul

echo.
echo Project cleanup completed!
echo.
echo Cleaned items:
echo   - Executable files
echo   - Build directories
echo   - Release packages
echo   - Log files
echo   - Temporary files
echo   - Test artifacts
echo.

pause
