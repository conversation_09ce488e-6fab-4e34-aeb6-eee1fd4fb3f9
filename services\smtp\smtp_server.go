package smtp

import (
	"fmt"
	"io"
	"log"
	"net"

	"github.com/emersion/go-smtp"
	"github.com/beego/beego/v2/server/web"
	"goemail/services/email"
)

// Backend SMTP后端实现
type Backend struct {
	emailService *email.EmailService
}

// NewBackend 创建SMTP后端
func NewBackend() *Backend {
	return &Backend{
		emailService: email.NewEmailService(),
	}
}

// NewSession 创建新的SMTP会话
func (bkd *Backend) NewSession(conn *smtp.Conn) (smtp.Session, error) {
	return &Session{
		backend: bkd,
		conn:    conn,
	}, nil
}

// Session SMTP会话实现
type Session struct {
	backend *Backend
	conn    *smtp.Conn
	from    string
	to      []string
}

// AuthPlain 处理PLAIN认证（临时邮箱不需要认证）
func (s *Session) AuthPlain(username, password string) error {
	// 临时邮箱系统不需要认证，直接返回成功
	return nil
}

// Mail 处理MAIL FROM命令
func (s *Session) Mail(from string, opts *smtp.MailOptions) error {
	log.Printf("SMTP: Mail from: %s", from)
	s.from = from
	return nil
}

// Rcpt 处理RCPT TO命令
func (s *Session) Rcpt(to string, opts *smtp.RcptOptions) error {
	log.Printf("SMTP: Rcpt to: %s", to)

	// 验证收件人地址是否属于我们的域名
	if !s.backend.emailService.IsValidDomain(to) {
		return fmt.Errorf("domain not supported")
	}

	s.to = append(s.to, to)
	return nil
}

// Data 处理DATA命令，接收邮件内容
func (s *Session) Data(r io.Reader) error {
	log.Printf("SMTP: Receiving email data from %s to %v", s.from, s.to)
	
	// 读取邮件内容
	emailData, err := io.ReadAll(r)
	if err != nil {
		return fmt.Errorf("failed to read email data: %v", err)
	}
	
	// 处理每个收件人
	for _, toAddress := range s.to {
		err = s.backend.emailService.ProcessIncomingEmail(emailData, toAddress)
		if err != nil {
			log.Printf("Failed to process email for %s: %v", toAddress, err)
			// 继续处理其他收件人，不中断
		} else {
			log.Printf("Email successfully processed for %s", toAddress)
		}
	}
	
	return nil
}

// Reset 重置会话
func (s *Session) Reset() {
	s.from = ""
	s.to = nil
}

// Logout 登出会话
func (s *Session) Logout() error {
	return nil
}

// StartSMTPServer 启动SMTP服务器
func StartSMTPServer() error {
	// 获取SMTP端口配置
	smtpPort, err := web.AppConfig.String("smtp_port")
	if err != nil {
		smtpPort = "2525" // 默认端口
	}
	
	// 创建SMTP服务器
	backend := NewBackend()
	server := smtp.NewServer(backend)
	
	// 配置服务器
	server.Addr = ":" + smtpPort
	server.Domain = "yourdomain.com"
	server.WriteTimeout = 10 * 60 // 10分钟
	server.ReadTimeout = 10 * 60  // 10分钟
	server.MaxMessageBytes = 10 * 1024 * 1024 // 10MB
	server.MaxRecipients = 50
	server.AllowInsecureAuth = true // 允许非加密认证（开发环境）
	
	log.Printf("Starting SMTP server on port %s", smtpPort)
	
	// 启动服务器
	listener, err := net.Listen("tcp", server.Addr)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %v", server.Addr, err)
	}
	
	return server.Serve(listener)
}

// SMTPConfig SMTP服务器配置
type SMTPConfig struct {
	Port           string
	Domain         string
	MaxMessageSize int64
	MaxRecipients  int
	WriteTimeout   int
	ReadTimeout    int
}

// GetSMTPConfig 获取SMTP配置
func GetSMTPConfig() *SMTPConfig {
	port, _ := web.AppConfig.String("smtp_port")
	if port == "" {
		port = "2525"
	}
	
	domain, _ := web.AppConfig.String("mail_domain")
	if domain == "" {
		domain = "yourdomain.com"
	}
	
	return &SMTPConfig{
		Port:           port,
		Domain:         domain,
		MaxMessageSize: 10 * 1024 * 1024, // 10MB
		MaxRecipients:  50,
		WriteTimeout:   600, // 10分钟
		ReadTimeout:    600, // 10分钟
	}
}
