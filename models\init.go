package models

import (
	"database/sql"
	"fmt"
	"log"

	"github.com/beego/beego/v2/client/orm"
	"github.com/beego/beego/v2/server/web"
	_ "github.com/go-sql-driver/mysql"
)

func init() {
	// 注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)
	
	// 注册模型
	orm.RegisterModel(new(Email), new(Attachment))
	
	// 获取数据库配置
	dbHost, _ := web.AppConfig.String("db_host")
	dbPort, _ := web.AppConfig.String("db_port")
	dbUser, _ := web.AppConfig.String("db_user")
	dbPassword, _ := web.AppConfig.String("db_password")
	dbName, _ := web.AppConfig.String("db_name")
	dbCharset, _ := web.AppConfig.String("db_charset")
	
	// 构建数据库连接字符串
	dataSource := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=true&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName, dbCharset)
	
	// 注册数据库
	err := orm.RegisterDataBase("default", "mysql", dataSource)
	if err != nil {
		log.Fatal("Failed to register database:", err)
	}
	
	// 开发模式下自动创建表
	runmode, _ := web.AppConfig.String("runmode")
	if runmode == "dev" {
		// 创建数据库（如果不存在）
		createDatabase(dbUser, dbPassword, dbHost, dbPort, dbName)
		
		// 同步数据库表结构
		err = orm.RunSyncdb("default", false, true)
		if err != nil {
			log.Fatal("Failed to sync database:", err)
		}
	}
}

func createDatabase(user, password, host, port, dbName string) {
	// 连接MySQL服务器（不指定数据库）
	dataSource := fmt.Sprintf("%s:%s@tcp(%s:%s)/", user, password, host, port)
	db, err := sql.Open("mysql", dataSource)
	if err != nil {
		log.Fatal("Failed to connect to MySQL server:", err)
	}
	defer db.Close()
	
	// 创建数据库
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", dbName))
	if err != nil {
		log.Fatal("Failed to create database:", err)
	}
	
	log.Printf("Database %s created or already exists", dbName)
}
