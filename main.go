package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/beego/beego/v2/server/web"
	_ "temp-email-system/routers"
	"temp-email-system/services/cache"
	"temp-email-system/services/smtp"
	"temp-email-system/services/imap"
	"temp-email-system/services/pop3"
)

// 版本信息（编译时注入）
var (
	Version   = "1.0.0"
	BuildTime = "unknown"
	GitCommit = "unknown"
)

func main() {
	// 解析命令行参数
	var showVersion = flag.Bool("version", false, "显示版本信息")
	var showHelp = flag.Bool("help", false, "显示帮助信息")
	flag.Parse()

	if *showVersion {
		printVersion()
		return
	}

	if *showHelp {
		printHelp()
		return
	}

	// 打印启动信息
	printStartupInfo()

	// 创建上下文用于优雅关闭
	_, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 初始化Redis缓存服务
	log.Println("初始化Redis缓存服务...")
	cache.InitRedisService()

	// 启动各种邮件服务
	var wg sync.WaitGroup
	errChan := make(chan error, 4) // 缓冲通道，避免阻塞

	// 启动SMTP服务器
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Println("Starting SMTP server...")
		if err := smtp.StartSMTPServer(); err != nil {
			select {
			case errChan <- fmt.Errorf("SMTP server error: %v", err):
			default:
			}
		}
	}()

	// 启动IMAP服务器
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Println("Starting IMAP server...")
		if err := imap.StartIMAPServer(); err != nil {
			select {
			case errChan <- fmt.Errorf("IMAP server error: %v", err):
			default:
			}
		}
	}()

	// 启动POP3服务器
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Println("Starting POP3 server...")
		if err := pop3.StartPOP3Server(); err != nil {
			select {
			case errChan <- fmt.Errorf("POP3 server error: %v", err):
			default:
			}
		}
	}()

	// 启动Beego Web服务器
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Println("Starting Beego web server...")
		web.Run()
	}()

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号或错误
	select {
	case sig := <-sigChan:
		log.Printf("收到信号 %v，开始优雅关闭...", sig)
	case err := <-errChan:
		log.Printf("服务错误: %v，开始关闭...", err)
	}

	// 开始优雅关闭
	gracefulShutdown(cancel, &wg)
}

// printVersion 打印版本信息
func printVersion() {
	fmt.Printf("临时邮箱系统 (Temporary Email System)\n")
	fmt.Printf("版本: %s\n", Version)
	fmt.Printf("构建时间: %s\n", BuildTime)
	fmt.Printf("Git提交: %s\n", GitCommit)
	fmt.Printf("Go版本: %s\n", "go1.21+")
}

// printHelp 打印帮助信息
func printHelp() {
	fmt.Printf("临时邮箱系统 (Temporary Email System)\n\n")
	fmt.Printf("用法:\n")
	fmt.Printf("  %s [选项]\n\n", "temp-email-system")
	fmt.Printf("选项:\n")
	fmt.Printf("  -version    显示版本信息\n")
	fmt.Printf("  -help       显示此帮助信息\n\n")
	fmt.Printf("服务端口:\n")
	fmt.Printf("  Web API:    http://localhost:8080\n")
	fmt.Printf("  SMTP:       localhost:2525\n")
	fmt.Printf("  IMAP:       localhost:1143\n")
	fmt.Printf("  POP3:       localhost:1110\n\n")
	fmt.Printf("配置文件:\n")
	fmt.Printf("  conf/app.conf - 主配置文件\n\n")
	fmt.Printf("更多信息请查看 README.md 文件\n")
}

// printStartupInfo 打印启动信息
func printStartupInfo() {
	fmt.Printf("\n")
	fmt.Printf("╔══════════════════════════════════════════════════════════════╗\n")
	fmt.Printf("║                    临时邮箱系统                              ║\n")
	fmt.Printf("║                Temporary Email System                        ║\n")
	fmt.Printf("╠══════════════════════════════════════════════════════════════╣\n")
	fmt.Printf("║ 版本: %-10s                                          ║\n", Version)
	fmt.Printf("║ 构建: %-20s                                  ║\n", BuildTime)
	fmt.Printf("║ 提交: %-10s                                          ║\n", GitCommit)
	fmt.Printf("╠══════════════════════════════════════════════════════════════╣\n")
	fmt.Printf("║ 服务端口:                                                   ║\n")
	fmt.Printf("║   Web API: http://localhost:8080                             ║\n")
	fmt.Printf("║   SMTP:    localhost:2525                                    ║\n")
	fmt.Printf("║   IMAP:    localhost:1143                                    ║\n")
	fmt.Printf("║   POP3:    localhost:1110                                    ║\n")
	fmt.Printf("╚══════════════════════════════════════════════════════════════╝\n")
	fmt.Printf("\n")
}

// gracefulShutdown 优雅关闭服务
func gracefulShutdown(cancel context.CancelFunc, wg *sync.WaitGroup) {
	log.Println("开始优雅关闭服务...")

	// 取消上下文，通知所有服务停止
	cancel()

	// 创建一个超时上下文，最多等待30秒
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 在goroutine中等待所有服务关闭
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		log.Println("所有服务已优雅关闭")
	case <-shutdownCtx.Done():
		log.Println("关闭超时，强制退出")
	}

	log.Println("程序退出")
}
